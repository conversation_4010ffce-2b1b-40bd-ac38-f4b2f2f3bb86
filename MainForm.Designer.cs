namespace BackupFileScanner
{
    partial class MainForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.groupBoxTarget = new System.Windows.Forms.GroupBox();
            this.btnLoadFromFile = new System.Windows.Forms.Button();
            this.txtUrlList = new System.Windows.Forms.TextBox();
            this.lblUrlList = new System.Windows.Forms.Label();
            this.txtSingleUrl = new System.Windows.Forms.TextBox();
            this.lblSingleUrl = new System.Windows.Forms.Label();
            this.radioUrlList = new System.Windows.Forms.RadioButton();
            this.radioSingleUrl = new System.Windows.Forms.RadioButton();
            this.groupBoxSettings = new System.Windows.Forms.GroupBox();
            this.txtCustomHeaders = new System.Windows.Forms.TextBox();
            this.lblCustomHeaders = new System.Windows.Forms.Label();
            this.numDelay = new System.Windows.Forms.NumericUpDown();
            this.lblDelay = new System.Windows.Forms.Label();
            this.numThreads = new System.Windows.Forms.NumericUpDown();
            this.lblThreads = new System.Windows.Forms.Label();
            this.numTimeout = new System.Windows.Forms.NumericUpDown();
            this.lblTimeout = new System.Windows.Forms.Label();
            this.chkIgnoreSSL = new System.Windows.Forms.CheckBox();
            this.chkRandomXFF = new System.Windows.Forms.CheckBox();
            this.radioGet = new System.Windows.Forms.RadioButton();
            this.radioHead = new System.Windows.Forms.RadioButton();
            this.lblHttpMethod = new System.Windows.Forms.Label();
            this.groupBoxResults = new System.Windows.Forms.GroupBox();
            this.listViewResults = new System.Windows.Forms.ListView();
            this.contextMenuResults = new System.Windows.Forms.ContextMenuStrip();
            this.menuItemCopyUrl = new System.Windows.Forms.ToolStripMenuItem();
            this.columnUrl = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnStatus = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnContentType = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnSize = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnTime = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnResult = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.groupBoxLog = new System.Windows.Forms.GroupBox();
            this.txtLog = new System.Windows.Forms.TextBox();
            this.btnStart = new System.Windows.Forms.Button();
            this.btnStop = new System.Windows.Forms.Button();
            this.btnClear = new System.Windows.Forms.Button();
            this.btnExport = new System.Windows.Forms.Button();
            this.statusStrip = new System.Windows.Forms.StatusStrip();
            this.toolStripStatusLabel = new System.Windows.Forms.ToolStripStatusLabel();
            this.toolStripProgressBar = new System.Windows.Forms.ToolStripProgressBar();
            this.openFileDialog = new System.Windows.Forms.OpenFileDialog();
            this.saveFileDialog = new System.Windows.Forms.SaveFileDialog();
            this.groupBoxTarget.SuspendLayout();
            this.groupBoxSettings.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numDelay)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numThreads)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numTimeout)).BeginInit();
            this.groupBoxResults.SuspendLayout();
            this.groupBoxLog.SuspendLayout();
            this.statusStrip.SuspendLayout();
            this.SuspendLayout();
            // 
            // groupBoxTarget
            // 
            this.groupBoxTarget.Controls.Add(this.btnLoadFromFile);
            this.groupBoxTarget.Controls.Add(this.txtUrlList);
            this.groupBoxTarget.Controls.Add(this.lblUrlList);
            this.groupBoxTarget.Controls.Add(this.txtSingleUrl);
            this.groupBoxTarget.Controls.Add(this.lblSingleUrl);
            this.groupBoxTarget.Controls.Add(this.radioUrlList);
            this.groupBoxTarget.Controls.Add(this.radioSingleUrl);
            this.groupBoxTarget.Location = new System.Drawing.Point(12, 12);
            this.groupBoxTarget.Name = "groupBoxTarget";
            this.groupBoxTarget.Size = new System.Drawing.Size(400, 150);
            this.groupBoxTarget.TabIndex = 0;
            this.groupBoxTarget.TabStop = false;
            this.groupBoxTarget.Text = "扫描目标";
            // 
            // btnLoadFromFile
            // 
            this.btnLoadFromFile.Location = new System.Drawing.Point(310, 120);
            this.btnLoadFromFile.Name = "btnLoadFromFile";
            this.btnLoadFromFile.Size = new System.Drawing.Size(75, 23);
            this.btnLoadFromFile.TabIndex = 6;
            this.btnLoadFromFile.Text = "从文件加载";
            this.btnLoadFromFile.UseVisualStyleBackColor = true;
            this.btnLoadFromFile.Click += new System.EventHandler(this.btnLoadFromFile_Click);
            // 
            // txtUrlList
            // 
            this.txtUrlList.Location = new System.Drawing.Point(80, 70);
            this.txtUrlList.Multiline = true;
            this.txtUrlList.Name = "txtUrlList";
            this.txtUrlList.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
            this.txtUrlList.Size = new System.Drawing.Size(305, 44);
            this.txtUrlList.TabIndex = 5;
            // 
            // lblUrlList
            // 
            this.lblUrlList.AutoSize = true;
            this.lblUrlList.Location = new System.Drawing.Point(20, 73);
            this.lblUrlList.Name = "lblUrlList";
            this.lblUrlList.Size = new System.Drawing.Size(53, 12);
            this.lblUrlList.TabIndex = 4;
            this.lblUrlList.Text = "URL列表:";
            // 
            // txtSingleUrl
            // 
            this.txtSingleUrl.Location = new System.Drawing.Point(80, 43);
            this.txtSingleUrl.Name = "txtSingleUrl";
            this.txtSingleUrl.Size = new System.Drawing.Size(305, 21);
            this.txtSingleUrl.TabIndex = 3;
            // 
            // lblSingleUrl
            // 
            this.lblSingleUrl.AutoSize = true;
            this.lblSingleUrl.Location = new System.Drawing.Point(20, 46);
            this.lblSingleUrl.Name = "lblSingleUrl";
            this.lblSingleUrl.Size = new System.Drawing.Size(53, 12);
            this.lblSingleUrl.TabIndex = 2;
            this.lblSingleUrl.Text = "单个URL:";
            // 
            // radioUrlList
            // 
            this.radioUrlList.AutoSize = true;
            this.radioUrlList.Location = new System.Drawing.Point(200, 20);
            this.radioUrlList.Name = "radioUrlList";
            this.radioUrlList.Size = new System.Drawing.Size(71, 16);
            this.radioUrlList.TabIndex = 1;
            this.radioUrlList.Text = "URL列表";
            this.radioUrlList.UseVisualStyleBackColor = true;
            this.radioUrlList.CheckedChanged += new System.EventHandler(this.radioUrlList_CheckedChanged);
            // 
            // radioSingleUrl
            // 
            this.radioSingleUrl.AutoSize = true;
            this.radioSingleUrl.Checked = true;
            this.radioSingleUrl.Location = new System.Drawing.Point(20, 20);
            this.radioSingleUrl.Name = "radioSingleUrl";
            this.radioSingleUrl.Size = new System.Drawing.Size(71, 16);
            this.radioSingleUrl.TabIndex = 0;
            this.radioSingleUrl.TabStop = true;
            this.radioSingleUrl.Text = "单个URL";
            this.radioSingleUrl.UseVisualStyleBackColor = true;
            this.radioSingleUrl.CheckedChanged += new System.EventHandler(this.radioSingleUrl_CheckedChanged);
            // 
            // groupBoxSettings
            // 
            this.groupBoxSettings.Controls.Add(this.txtCustomHeaders);
            this.groupBoxSettings.Controls.Add(this.lblCustomHeaders);
            this.groupBoxSettings.Controls.Add(this.numDelay);
            this.groupBoxSettings.Controls.Add(this.lblDelay);
            this.groupBoxSettings.Controls.Add(this.numThreads);
            this.groupBoxSettings.Controls.Add(this.lblThreads);
            this.groupBoxSettings.Controls.Add(this.numTimeout);
            this.groupBoxSettings.Controls.Add(this.lblTimeout);
            this.groupBoxSettings.Controls.Add(this.chkIgnoreSSL);
            this.groupBoxSettings.Controls.Add(this.chkRandomXFF);
            this.groupBoxSettings.Controls.Add(this.radioGet);
            this.groupBoxSettings.Controls.Add(this.radioHead);
            this.groupBoxSettings.Controls.Add(this.lblHttpMethod);
            this.groupBoxSettings.Location = new System.Drawing.Point(430, 12);
            this.groupBoxSettings.Name = "groupBoxSettings";
            this.groupBoxSettings.Size = new System.Drawing.Size(350, 200);
            this.groupBoxSettings.TabIndex = 1;
            this.groupBoxSettings.TabStop = false;
            this.groupBoxSettings.Text = "扫描设置";
            //
            // txtCustomHeaders
            //
            this.txtCustomHeaders.Location = new System.Drawing.Point(80, 120);
            this.txtCustomHeaders.Multiline = true;
            this.txtCustomHeaders.Name = "txtCustomHeaders";
            this.txtCustomHeaders.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
            this.txtCustomHeaders.Size = new System.Drawing.Size(250, 50);
            this.txtCustomHeaders.TabIndex = 11;
            this.txtCustomHeaders.Text = "Accept: */*\r\nAccept-Encoding: gzip, deflate";
            //
            // lblCustomHeaders
            //
            this.lblCustomHeaders.AutoSize = true;
            this.lblCustomHeaders.Location = new System.Drawing.Point(15, 120);
            this.lblCustomHeaders.Name = "lblCustomHeaders";
            this.lblCustomHeaders.Size = new System.Drawing.Size(59, 12);
            this.lblCustomHeaders.TabIndex = 10;
            this.lblCustomHeaders.Text = "自定义头:";
            //
            // numDelay
            //
            this.numDelay.DecimalPlaces = 1;
            this.numDelay.Increment = new decimal(new int[] {
            1,
            0,
            0,
            65536});
            this.numDelay.Location = new System.Drawing.Point(250, 95);
            this.numDelay.Maximum = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.numDelay.Name = "numDelay";
            this.numDelay.Size = new System.Drawing.Size(80, 21);
            this.numDelay.TabIndex = 9;
            this.numDelay.Value = new decimal(new int[] {
            1,
            0,
            0,
            65536});
            //
            // lblDelay
            //
            this.lblDelay.AutoSize = true;
            this.lblDelay.Location = new System.Drawing.Point(180, 97);
            this.lblDelay.Name = "lblDelay";
            this.lblDelay.Size = new System.Drawing.Size(65, 12);
            this.lblDelay.TabIndex = 8;
            this.lblDelay.Text = "延迟(秒):";
            //
            // numThreads
            //
            this.numThreads.Location = new System.Drawing.Point(80, 95);
            this.numThreads.Maximum = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.numThreads.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numThreads.Name = "numThreads";
            this.numThreads.Size = new System.Drawing.Size(80, 21);
            this.numThreads.TabIndex = 7;
            this.numThreads.Value = new decimal(new int[] {
            10,
            0,
            0,
            0});
            //
            // lblThreads
            //
            this.lblThreads.AutoSize = true;
            this.lblThreads.Location = new System.Drawing.Point(15, 97);
            this.lblThreads.Name = "lblThreads";
            this.lblThreads.Size = new System.Drawing.Size(53, 12);
            this.lblThreads.TabIndex = 6;
            this.lblThreads.Text = "线程数:";
            //
            // numTimeout
            //
            this.numTimeout.Increment = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numTimeout.Location = new System.Drawing.Point(250, 70);
            this.numTimeout.Maximum = new decimal(new int[] {
            300,
            0,
            0,
            0});
            this.numTimeout.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numTimeout.Name = "numTimeout";
            this.numTimeout.Size = new System.Drawing.Size(80, 21);
            this.numTimeout.TabIndex = 5;
            this.numTimeout.Value = new decimal(new int[] {
            10,
            0,
            0,
            0});
            //
            // lblTimeout
            //
            this.lblTimeout.AutoSize = true;
            this.lblTimeout.Location = new System.Drawing.Point(180, 72);
            this.lblTimeout.Name = "lblTimeout";
            this.lblTimeout.Size = new System.Drawing.Size(65, 12);
            this.lblTimeout.TabIndex = 4;
            this.lblTimeout.Text = "超时(秒):";
            //
            // chkIgnoreSSL
            //
            this.chkIgnoreSSL.AutoSize = true;
            this.chkIgnoreSSL.Checked = true;
            this.chkIgnoreSSL.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkIgnoreSSL.Location = new System.Drawing.Point(15, 72);
            this.chkIgnoreSSL.Name = "chkIgnoreSSL";
            this.chkIgnoreSSL.Size = new System.Drawing.Size(84, 16);
            this.chkIgnoreSSL.TabIndex = 3;
            this.chkIgnoreSSL.Text = "忽略SSL错误";
            this.chkIgnoreSSL.UseVisualStyleBackColor = true;
            //
            // chkRandomXFF
            //
            this.chkRandomXFF.AutoSize = true;
            this.chkRandomXFF.Location = new System.Drawing.Point(15, 145);
            this.chkRandomXFF.Name = "chkRandomXFF";
            this.chkRandomXFF.Size = new System.Drawing.Size(156, 16);
            this.chkRandomXFF.TabIndex = 12;
            this.chkRandomXFF.Text = "随机X-Forwarded-For头";
            this.chkRandomXFF.UseVisualStyleBackColor = true;
            //
            // radioGet
            //
            this.radioGet.AutoSize = true;
            this.radioGet.Location = new System.Drawing.Point(130, 45);
            this.radioGet.Name = "radioGet";
            this.radioGet.Size = new System.Drawing.Size(41, 16);
            this.radioGet.TabIndex = 2;
            this.radioGet.Text = "GET";
            this.radioGet.UseVisualStyleBackColor = true;
            //
            // radioHead
            //
            this.radioHead.AutoSize = true;
            this.radioHead.Checked = true;
            this.radioHead.Location = new System.Drawing.Point(80, 45);
            this.radioHead.Name = "radioHead";
            this.radioHead.Size = new System.Drawing.Size(47, 16);
            this.radioHead.TabIndex = 1;
            this.radioHead.TabStop = true;
            this.radioHead.Text = "HEAD";
            this.radioHead.UseVisualStyleBackColor = true;
            //
            // lblHttpMethod
            //
            this.lblHttpMethod.AutoSize = true;
            this.lblHttpMethod.Location = new System.Drawing.Point(15, 47);
            this.lblHttpMethod.Name = "lblHttpMethod";
            this.lblHttpMethod.Size = new System.Drawing.Size(59, 12);
            this.lblHttpMethod.TabIndex = 0;
            this.lblHttpMethod.Text = "请求方式:";
            //
            // groupBoxResults
            //
            this.groupBoxResults.Controls.Add(this.listViewResults);
            this.groupBoxResults.Location = new System.Drawing.Point(12, 260);
            this.groupBoxResults.Name = "groupBoxResults";
            this.groupBoxResults.Size = new System.Drawing.Size(768, 200);
            this.groupBoxResults.TabIndex = 2;
            this.groupBoxResults.TabStop = false;
            this.groupBoxResults.Text = "扫描结果";
            //
            // listViewResults
            //
            this.listViewResults.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnUrl,
            this.columnStatus,
            this.columnContentType,
            this.columnSize,
            this.columnTime,
            this.columnResult});
            this.listViewResults.ContextMenuStrip = this.contextMenuResults;
            this.listViewResults.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listViewResults.FullRowSelect = true;
            this.listViewResults.GridLines = true;
            this.listViewResults.Location = new System.Drawing.Point(3, 17);
            this.listViewResults.Name = "listViewResults";
            this.listViewResults.Size = new System.Drawing.Size(762, 180);
            this.listViewResults.TabIndex = 0;
            this.listViewResults.UseCompatibleStateImageBehavior = false;
            this.listViewResults.View = System.Windows.Forms.View.Details;
            this.listViewResults.DoubleClick += new System.EventHandler(this.listViewResults_DoubleClick);
            //
            // contextMenuResults
            //
            this.contextMenuResults.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.menuItemCopyUrl});
            this.contextMenuResults.Name = "contextMenuResults";
            this.contextMenuResults.Size = new System.Drawing.Size(153, 26);
            //
            // menuItemCopyUrl
            //
            this.menuItemCopyUrl.Name = "menuItemCopyUrl";
            this.menuItemCopyUrl.Size = new System.Drawing.Size(152, 22);
            this.menuItemCopyUrl.Text = "复制链接";
            this.menuItemCopyUrl.Click += new System.EventHandler(this.menuItemCopyUrl_Click);
            //
            // columnUrl
            //
            this.columnUrl.Text = "URL";
            this.columnUrl.Width = 300;
            //
            // columnStatus
            //
            this.columnStatus.Text = "状态码";
            this.columnStatus.Width = 60;
            //
            // columnContentType
            //
            this.columnContentType.Text = "内容类型";
            this.columnContentType.Width = 120;
            //
            // columnSize
            //
            this.columnSize.Text = "大小";
            this.columnSize.Width = 80;
            //
            // columnTime
            //
            this.columnTime.Text = "响应时间";
            this.columnTime.Width = 80;
            //
            // columnResult
            //
            this.columnResult.Text = "检测结果";
            this.columnResult.Width = 100;
            //
            // groupBoxLog
            //
            this.groupBoxLog.Controls.Add(this.txtLog);
            this.groupBoxLog.Location = new System.Drawing.Point(12, 470);
            this.groupBoxLog.Name = "groupBoxLog";
            this.groupBoxLog.Size = new System.Drawing.Size(768, 100);
            this.groupBoxLog.TabIndex = 3;
            this.groupBoxLog.TabStop = false;
            this.groupBoxLog.Text = "日志信息";
            //
            // txtLog
            //
            this.txtLog.Dock = System.Windows.Forms.DockStyle.Fill;
            this.txtLog.Location = new System.Drawing.Point(3, 17);
            this.txtLog.Multiline = true;
            this.txtLog.Name = "txtLog";
            this.txtLog.ReadOnly = true;
            this.txtLog.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
            this.txtLog.Size = new System.Drawing.Size(762, 80);
            this.txtLog.TabIndex = 0;
            //
            // btnStart
            //
            this.btnStart.Location = new System.Drawing.Point(12, 210);
            this.btnStart.Name = "btnStart";
            this.btnStart.Size = new System.Drawing.Size(75, 30);
            this.btnStart.TabIndex = 4;
            this.btnStart.Text = "开始扫描";
            this.btnStart.UseVisualStyleBackColor = true;
            this.btnStart.Click += new System.EventHandler(this.btnStart_Click);
            //
            // btnStop
            //
            this.btnStop.Enabled = false;
            this.btnStop.Location = new System.Drawing.Point(100, 210);
            this.btnStop.Name = "btnStop";
            this.btnStop.Size = new System.Drawing.Size(75, 30);
            this.btnStop.TabIndex = 5;
            this.btnStop.Text = "停止扫描";
            this.btnStop.UseVisualStyleBackColor = true;
            this.btnStop.Click += new System.EventHandler(this.btnStop_Click);
            //
            // btnClear
            //
            this.btnClear.Location = new System.Drawing.Point(188, 210);
            this.btnClear.Name = "btnClear";
            this.btnClear.Size = new System.Drawing.Size(75, 30);
            this.btnClear.TabIndex = 6;
            this.btnClear.Text = "清空结果";
            this.btnClear.UseVisualStyleBackColor = true;
            this.btnClear.Click += new System.EventHandler(this.btnClear_Click);
            //
            // btnExport
            //
            this.btnExport.Location = new System.Drawing.Point(276, 210);
            this.btnExport.Name = "btnExport";
            this.btnExport.Size = new System.Drawing.Size(75, 30);
            this.btnExport.TabIndex = 7;
            this.btnExport.Text = "导出结果";
            this.btnExport.UseVisualStyleBackColor = true;
            this.btnExport.Click += new System.EventHandler(this.btnExport_Click);
            //
            // statusStrip
            //
            this.statusStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripStatusLabel,
            this.toolStripProgressBar});
            this.statusStrip.Location = new System.Drawing.Point(0, 550);
            this.statusStrip.Name = "statusStrip";
            this.statusStrip.Size = new System.Drawing.Size(792, 22);
            this.statusStrip.TabIndex = 8;
            this.statusStrip.Text = "statusStrip1";
            //
            // toolStripStatusLabel
            //
            this.toolStripStatusLabel.Name = "toolStripStatusLabel";
            this.toolStripStatusLabel.Size = new System.Drawing.Size(32, 17);
            this.toolStripStatusLabel.Text = "就绪";
            //
            // toolStripProgressBar
            //
            this.toolStripProgressBar.Name = "toolStripProgressBar";
            this.toolStripProgressBar.Size = new System.Drawing.Size(200, 16);
            //
            // openFileDialog
            //
            this.openFileDialog.Filter = "文本文件|*.txt|所有文件|*.*";
            this.openFileDialog.Title = "选择URL列表文件";
            //
            // saveFileDialog
            //
            this.saveFileDialog.Filter = "CSV文件|*.csv|文本文件|*.txt|所有文件|*.*";
            this.saveFileDialog.Title = "导出扫描结果";
            //
            // MainForm
            //
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(792, 602);
            this.Controls.Add(this.statusStrip);
            this.Controls.Add(this.btnExport);
            this.Controls.Add(this.btnClear);
            this.Controls.Add(this.btnStop);
            this.Controls.Add(this.btnStart);
            this.Controls.Add(this.groupBoxLog);
            this.Controls.Add(this.groupBoxResults);
            this.Controls.Add(this.groupBoxSettings);
            this.Controls.Add(this.groupBoxTarget);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle;
            this.MaximizeBox = false;
            this.Name = "MainForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "网站备份文件泄露检测工具 v1.0";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.MainForm_FormClosing);
            this.groupBoxTarget.ResumeLayout(false);
            this.groupBoxTarget.PerformLayout();
            this.groupBoxSettings.ResumeLayout(false);
            this.groupBoxSettings.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numDelay)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numThreads)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numTimeout)).EndInit();
            this.groupBoxResults.ResumeLayout(false);
            this.groupBoxLog.ResumeLayout(false);
            this.groupBoxLog.PerformLayout();
            this.statusStrip.ResumeLayout(false);
            this.statusStrip.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();
        }

        #endregion

        private System.Windows.Forms.GroupBox groupBoxTarget;
        private System.Windows.Forms.Button btnLoadFromFile;
        private System.Windows.Forms.TextBox txtUrlList;
        private System.Windows.Forms.Label lblUrlList;
        private System.Windows.Forms.TextBox txtSingleUrl;
        private System.Windows.Forms.Label lblSingleUrl;
        private System.Windows.Forms.RadioButton radioUrlList;
        private System.Windows.Forms.RadioButton radioSingleUrl;
        private System.Windows.Forms.GroupBox groupBoxSettings;
        private System.Windows.Forms.TextBox txtCustomHeaders;
        private System.Windows.Forms.Label lblCustomHeaders;
        private System.Windows.Forms.NumericUpDown numDelay;
        private System.Windows.Forms.Label lblDelay;
        private System.Windows.Forms.NumericUpDown numThreads;
        private System.Windows.Forms.Label lblThreads;
        private System.Windows.Forms.NumericUpDown numTimeout;
        private System.Windows.Forms.Label lblTimeout;
        private System.Windows.Forms.CheckBox chkIgnoreSSL;
        private System.Windows.Forms.CheckBox chkRandomXFF;
        private System.Windows.Forms.RadioButton radioGet;
        private System.Windows.Forms.RadioButton radioHead;
        private System.Windows.Forms.Label lblHttpMethod;
        private System.Windows.Forms.GroupBox groupBoxResults;
        private System.Windows.Forms.ListView listViewResults;
        private System.Windows.Forms.ContextMenuStrip contextMenuResults;
        private System.Windows.Forms.ToolStripMenuItem menuItemCopyUrl;
        private System.Windows.Forms.ColumnHeader columnUrl;
        private System.Windows.Forms.ColumnHeader columnStatus;
        private System.Windows.Forms.ColumnHeader columnContentType;
        private System.Windows.Forms.ColumnHeader columnSize;
        private System.Windows.Forms.ColumnHeader columnTime;
        private System.Windows.Forms.ColumnHeader columnResult;
        private System.Windows.Forms.GroupBox groupBoxLog;
        private System.Windows.Forms.TextBox txtLog;
        private System.Windows.Forms.Button btnStart;
        private System.Windows.Forms.Button btnStop;
        private System.Windows.Forms.Button btnClear;
        private System.Windows.Forms.Button btnExport;
        private System.Windows.Forms.StatusStrip statusStrip;
        private System.Windows.Forms.ToolStripStatusLabel toolStripStatusLabel;
        private System.Windows.Forms.ToolStripProgressBar toolStripProgressBar;
        private System.Windows.Forms.OpenFileDialog openFileDialog;
        private System.Windows.Forms.SaveFileDialog saveFileDialog;
    }
}
