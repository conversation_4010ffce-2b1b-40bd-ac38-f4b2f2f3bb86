using System;
using System.Threading.Tasks;

namespace BackupFileScanner
{
    /// <summary>
    /// 性能测试控制台程序
    /// </summary>
    class PerformanceTestProgram
    {
        static void Main(string[] args)
        {
            Console.WriteLine("BackupFileScanner 性能测试工具");
            Console.WriteLine("================================");
            Console.WriteLine();

            var performanceTest = new PerformanceTest();

            try
            {
                // 内存使用测试
                performanceTest.TestMemoryUsage();
                Console.WriteLine();

                // 性能测试
                var task = performanceTest.RunPerformanceTestAsync();
                task.Wait();

                Console.WriteLine();
                Console.WriteLine("所有测试完成！");
            }
            catch (Exception ex)
            {
                Console.WriteLine(string.Format("测试过程中发生错误: {0}", ex.Message));
            }
            finally
            {
                performanceTest.Dispose();
            }

            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }
    }
}
