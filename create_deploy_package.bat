@echo off
echo ========================================
echo BackupFileScanner Deployment Package Creator
echo ========================================
echo.

set DEPLOY_DIR=BackupFileScanner_Deploy
set SOURCE_DIR=bin\Release

echo Creating deployment package...
echo.

REM Remove old deployment directory
if exist "%DEPLOY_DIR%" (
    echo Removing old deployment directory...
    rmdir /s /q "%DEPLOY_DIR%"
)

REM Create deployment directory
mkdir "%DEPLOY_DIR%"

REM Check if source files exist
if not exist "%SOURCE_DIR%\BackupFileScanner.exe" (
    echo [ERROR] BackupFileScanner.exe not found
    echo Please run build_en.bat first to compile the program
    pause
    exit /b 1
)

echo Copying main program...
copy "%SOURCE_DIR%\BackupFileScanner.exe" "%DEPLOY_DIR%\" >nul

echo Copying config file...
copy "%SOURCE_DIR%\BackupFileScanner.exe.config" "%DEPLOY_DIR%\" >nul

echo Copying dictionary files...
if not exist "%DEPLOY_DIR%\dict" mkdir "%DEPLOY_DIR%\dict"
copy "%SOURCE_DIR%\dict\*.dict" "%DEPLOY_DIR%\dict\" >nul

echo Copying documentation...
copy "README.md" "%DEPLOY_DIR%\" >nul 2>&1
copy "PERFORMANCE_OPTIMIZATION_REPORT.md" "%DEPLOY_DIR%\" >nul 2>&1

REM Create deployment instructions
echo Creating deployment instructions...
(
echo BackupFileScanner Deployment Instructions
echo =========================================
echo.
echo System Requirements:
echo - Windows 7/8/10/11
echo - .NET Framework 4.7.2 or higher
echo.
echo File Description:
echo - BackupFileScanner.exe        : Main program
echo - BackupFileScanner.exe.config : Configuration file ^(REQUIRED^)
echo - dict/                        : Dictionary folder ^(REQUIRED^)
echo   - dir.dict                   : Directory name dictionary
echo   - archiver.dict              : Archive file extension dictionary
echo   - UA.dict                    : User-Agent dictionary
echo.
echo Usage:
echo 1. Copy the entire folder to target computer
echo 2. Ensure .NET Framework 4.7.2+ is installed on target computer
echo 3. Double-click BackupFileScanner.exe to run
echo.
echo Important Notes:
echo - ALL files must be kept in the same directory
echo - Do NOT delete or modify the .config file
echo - Dictionary files in dict folder are essential
echo.
echo For more information, see README.md
) > "%DEPLOY_DIR%\DEPLOYMENT_INSTRUCTIONS.txt"

REM Display deployment package contents
echo.
echo ========================================
echo Deployment package created successfully!
echo ========================================
echo.
echo Package location: %DEPLOY_DIR%\
echo.
echo Package contents:
dir "%DEPLOY_DIR%" /b
echo.
echo Dictionary files:
dir "%DEPLOY_DIR%\dict" /b
echo.
echo You can now copy the %DEPLOY_DIR% folder to any computer.
echo.
pause
