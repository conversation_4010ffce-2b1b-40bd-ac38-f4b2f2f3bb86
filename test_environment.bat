@echo off
echo ========================================
echo BackupFileScanner - Environment Test
echo ========================================
echo.

echo 1. Checking .NET Framework...
reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\NET Framework Setup\NDP\v4\Full\" /v Release 2>nul
if %ERRORLEVEL% EQU 0 (
    echo [OK] .NET Framework 4.x is installed
) else (
    echo [FAIL] .NET Framework 4.x not found
)
echo.

echo 2. Checking compiler...
if exist "C:\Windows\Microsoft.NET\Framework64\v4.0.30319\csc.exe" (
    echo [OK] C# compiler found
) else (
    echo [FAIL] C# compiler not found
)
echo.

echo 3. Checking project files...
if exist "BackupFileScanner.csproj" (
    echo [OK] Project file exists
) else (
    echo [FAIL] Project file not found
)

if exist "MainForm.cs" (
    echo [OK] MainForm file exists
) else (
    echo [FAIL] MainForm file not found
)

if exist "BackupFileDetector.cs" (
    echo [OK] Core detector file exists
) else (
    echo [FAIL] Core detector file not found
)
echo.

echo 4. Checking dictionary files...
if exist "dict\dir.dict" (
    echo [OK] Directory dictionary exists
) else (
    echo [FAIL] Directory dictionary not found
)

if exist "dict\archiver.dict" (
    echo [OK] Archive dictionary exists
) else (
    echo [FAIL] Archive dictionary not found
)

if exist "dict\UA.dict" (
    echo [OK] User-Agent dictionary exists
) else (
    echo [FAIL] User-Agent dictionary not found
)
echo.

echo 5. Checking build output...
if exist "bin\Release\BackupFileScanner.exe" (
    echo [OK] Executable file generated
    echo   File size:
    dir "bin\Release\BackupFileScanner.exe" | findstr "BackupFileScanner.exe"
) else (
    echo [FAIL] Executable file not found
)

if exist "bin\Release\dict" (
    echo [OK] Dictionary files copied to output
) else (
    echo [FAIL] Dictionary files not copied
)
echo.

echo 6. Testing program startup...
echo Testing program startup (will close after 3 seconds)...
start "" "bin\Release\BackupFileScanner.exe"
timeout /t 3 /nobreak >nul
tasklist | findstr "BackupFileScanner" >nul
if %ERRORLEVEL% EQU 0 (
    echo [OK] Program can start normally
    taskkill /f /im BackupFileScanner.exe >nul 2>&1
) else (
    echo [INFO] Program startup test completed
)
echo.

echo ========================================
echo Environment test completed!
echo ========================================
echo.
echo If all items show [OK], the environment is configured correctly.
echo You can run the following command for full testing:
echo   .\bin\Release\BackupFileScanner.exe
echo.
pause
