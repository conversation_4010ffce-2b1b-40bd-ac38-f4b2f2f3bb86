# BackupFileScanner 功能更新说明

## 🆕 新功能更新

### 1. 超时和延时单位优化

**更新内容：**
- 将超时时间和延迟时间的单位从毫秒改为秒
- 界面显示更加直观，便于用户理解和配置

**具体变化：**

| 设置项 | 原来 | 现在 |
|--------|------|------|
| 超时时间 | 1000-60000毫秒 | 1-300秒 |
| 延迟时间 | 100-5000毫秒 | 0.1-10秒 |
| 界面标签 | "超时(ms)" | "超时(秒)" |
| 界面标签 | "延迟(ms)" | "延迟(秒)" |
| 默认超时 | 10000毫秒 | 10秒 |
| 默认延迟 | 100毫秒 | 0.1秒 |

**使用建议：**
```
一般扫描：超时 10秒，延迟 0.1秒
快速扫描：超时 5秒，延迟 0秒  
谨慎扫描：超时 30秒，延迟 1秒
大规模扫描：超时 15秒，延迟 0.5秒
```

### 2. 随机X-Forwarded-For头功能

**新增功能：**
- 在扫描设置区域新增"随机X-Forwarded-For头"复选框
- 默认不勾选，保持原有行为
- 勾选后为每个HTTP请求添加随机的X-Forwarded-For头

**功能特点：**
- **随机IP范围**：使用私有IP地址范围，避免影响真实网络
- **多种IP段**：支持4个常用私有IP段
- **安全性**：不会泄露真实IP信息

**支持的IP地址范围：**
```
******** - **************        (A类私有地址)
********** - **************      (B类私有地址)  
*********** - ***************    (C类私有地址)
127.0.0.1 - ***********          (本地回环地址)
```

**使用场景：**
- 绕过基于源IP的访问限制
- 模拟来自不同网络的请求
- 增加请求的随机性和隐蔽性
- 测试目标服务器的IP过滤机制

**示例X-Forwarded-For头：**
```
X-Forwarded-For: *************
X-Forwarded-For: *********
X-Forwarded-For: ************
X-Forwarded-For: 127.0.0.10
```

## 🔧 技术实现

### 时间单位转换
```csharp
// 在ApplySettings方法中进行转换
_detector.Timeout = (int)(numTimeout.Value * 1000); // 秒转毫秒
_detector.DelayBetweenRequests = (int)(numDelay.Value * 1000); // 秒转毫秒
```

### 随机IP生成
```csharp
private string GenerateRandomIP()
{
    // 定义私有IP地址范围
    var ipRanges = new[]
    {
        new { Start = new byte[] { 10, 0, 0, 1 }, End = new byte[] { 10, 255, 255, 254 } },
        new { Start = new byte[] { 172, 16, 0, 1 }, End = new byte[] { 172, 31, 255, 254 } },
        new { Start = new byte[] { 192, 168, 0, 1 }, End = new byte[] { 192, 168, 255, 254 } },
        new { Start = new byte[] { 127, 0, 0, 1 }, End = new byte[] { 127, 0, 0, 254 } }
    };

    // 随机选择IP范围并生成IP
    var selectedRange = ipRanges[_random.Next(ipRanges.Length)];
    var ip = new byte[4];
    
    for (int i = 0; i < 4; i++)
    {
        ip[i] = (byte)_random.Next(selectedRange.Start[i], selectedRange.End[i] + 1);
    }

    return string.Format("{0}.{1}.{2}.{3}", ip[0], ip[1], ip[2], ip[3]);
}
```

### X-Forwarded-For头添加
```csharp
// 在ScanUrlAsync方法中添加
if (UseRandomXForwardedFor)
{
    try
    {
        var randomIP = GenerateRandomIP();
        request.Headers.Add("X-Forwarded-For", randomIP);
    }
    catch (Exception ex)
    {
        OnLogMessage("添加X-Forwarded-For头失败: " + ex.Message);
    }
}
```

## 📋 界面变化

### 新增控件
- **chkRandomXFF**：随机X-Forwarded-For头复选框
- 位置：扫描设置组框内，SSL忽略选项下方

### 修改的控件
- **lblTimeout**：标签文本从"超时(ms)"改为"超时(秒)"
- **lblDelay**：标签文本从"延迟(ms)"改为"延迟(秒)"
- **numTimeout**：数值范围从1000-60000改为1-300
- **numDelay**：数值范围从100-5000改为0.1-10，支持小数

### 布局调整
- **groupBoxSettings**：高度从180增加到200，容纳新控件

## 🔄 兼容性

### 配置文件更新
- App.config中的默认值已更新为秒单位
- 旧版本配置文件需要手动调整数值

### 向后兼容
- 核心扫描逻辑保持不变
- 新功能默认关闭，不影响现有使用习惯
- 所有原有功能继续正常工作

## 🚀 使用建议

### 推荐配置
```
常规扫描：
- 超时：10秒
- 延迟：0.1秒
- 线程：10个
- X-Forwarded-For：不勾选

快速扫描：
- 超时：5秒
- 延迟：0秒
- 线程：20个
- X-Forwarded-For：不勾选

隐蔽扫描：
- 超时：15秒
- 延迟：1秒
- 线程：5个
- X-Forwarded-For：勾选

绕过IP限制：
- 超时：10秒
- 延迟：0.5秒
- 线程：10个
- X-Forwarded-For：勾选
```

## 📝 注意事项

1. **时间单位变化**：升级后需要重新调整超时和延迟设置
2. **X-Forwarded-For功能**：仅在需要时启用，避免不必要的请求头
3. **性能影响**：随机IP生成对性能影响微乎其微
4. **合规使用**：请确保在授权范围内使用此工具

这些更新使BackupFileScanner更加用户友好和功能强大，同时保持了良好的兼容性和性能表现。
