@echo off
echo 正在编译网站备份文件泄露检测工具...
echo.

set CSC_PATH="C:\Windows\Microsoft.NET\Framework64\v4.0.30319\csc.exe"

if not exist %CSC_PATH% (
    echo 错误: 未找到.NET Framework编译器
    echo 请确保已安装.NET Framework 4.7.2或更高版本
    pause
    exit /b 1
)

echo 使用编译器: %CSC_PATH%
echo.

REM 创建输出目录
if not exist "bin" mkdir "bin"
if not exist "bin\Release" mkdir "bin\Release"

REM 编译项目
%CSC_PATH% /target:winexe /out:bin\Release\BackupFileScanner.exe /reference:System.dll,System.Core.dll,System.Data.dll,System.Drawing.dll,System.Net.Http.dll,System.Windows.Forms.dll,System.Xml.dll,System.Configuration.dll Program.cs MainForm.cs MainForm.Designer.cs BackupFileDetector.cs ScanResult.cs Properties\AssemblyInfo.cs Properties\Resources.Designer.cs Properties\Settings.Designer.cs

if %ERRORLEVEL% EQU 0 (
    echo.
    echo 编译成功！
    echo 可执行文件位置: bin\Release\BackupFileScanner.exe
    echo.
    echo 正在复制配置文件和字典文件...
    copy "App.config" "bin\Release\BackupFileScanner.exe.config" >nul
    if not exist "bin\Release\dict" mkdir "bin\Release\dict"
    copy "dict\*.dict" "bin\Release\dict\" >nul
    echo 文件复制完成！
    echo.
    echo 按任意键运行程序...
    pause >nul
    start "" "bin\Release\BackupFileScanner.exe"
) else (
    echo.
    echo 编译失败！请检查错误信息。
    pause
)
