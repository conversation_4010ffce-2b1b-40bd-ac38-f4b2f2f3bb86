@echo off
echo Building Performance Test Tool...
echo.

REM Create output directory
if not exist "bin" mkdir "bin"
if not exist "bin\Release" mkdir "bin\Release"

echo Compiling performance test tool...
"C:\Windows\Microsoft.NET\Framework64\v4.0.30319\csc.exe" /target:exe /out:bin\Release\PerformanceTest.exe /reference:System.dll /reference:System.Core.dll /reference:System.Net.Http.dll BackupFileDetector.cs ScanResult.cs PerformanceTest.cs PerformanceTestProgram.cs

if %ERRORLEVEL% EQU 0 (
    echo.
    echo Build successful!
    echo Executable location: bin\Release\PerformanceTest.exe
    echo.
    echo Copying dictionary files...
    if not exist "bin\Release\dict" mkdir "bin\Release\dict"
    copy "dict\*.dict" "bin\Release\dict\" >nul 2>&1
    echo Dictionary files copied!
    echo.
    echo You can now run: bin\Release\PerformanceTest.exe
) else (
    echo.
    echo Build failed! Please check error messages above.
)

pause
