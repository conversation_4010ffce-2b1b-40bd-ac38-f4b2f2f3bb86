using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Threading;
using System.Threading.Tasks;

namespace BackupFileScanner
{
    /// <summary>
    /// 性能测试工具
    /// </summary>
    public class PerformanceTest
    {
        private BackupFileDetector _detector;
        private List<string> _testUrls;

        public PerformanceTest()
        {
            _detector = new BackupFileDetector();
            InitializeTestUrls();
        }

        private void InitializeTestUrls()
        {
            _testUrls = new List<string>
            {
                "https://httpbin.org",
                "https://www.example.com",
                "https://github.com",
                "https://stackoverflow.com",
                "https://www.google.com"
            };
        }

        /// <summary>
        /// 运行性能测试
        /// </summary>
        public async Task RunPerformanceTestAsync()
        {
            Console.WriteLine("=== BackupFileScanner 性能测试 ===");
            Console.WriteLine();

            // 生成测试URL
            var allTestUrls = new List<string>();
            foreach (var baseUrl in _testUrls)
            {
                var generatedUrls = _detector.GenerateBackupUrls(baseUrl);
                allTestUrls.AddRange(generatedUrls.GetRange(0, Math.Min(50, generatedUrls.Count))); // 每个域名取50个URL
            }

            Console.WriteLine(string.Format("生成测试URL数量: {0}", allTestUrls.Count));
            Console.WriteLine();

            // 测试不同的并发级别
            var concurrencyLevels = new[] { 5, 10, 20, 30, 50 };
            
            foreach (var concurrency in concurrencyLevels)
            {
                await TestConcurrencyLevel(allTestUrls, concurrency);
                Console.WriteLine();
                
                // 等待一段时间避免对服务器造成压力
                await Task.Delay(2000);
            }

            // 比较标准模式和高性能模式
            await ComparePerformanceModes(allTestUrls);
        }

        private async Task TestConcurrencyLevel(List<string> urls, int concurrency)
        {
            Console.WriteLine(string.Format("测试并发级别: {0}", concurrency));
            
            var stopwatch = Stopwatch.StartNew();
            var completedCount = 0;
            var foundCount = 0;

            _detector.ScanCompleted += (sender, result) =>
            {
                Interlocked.Increment(ref completedCount);
                if (result.IsBackupFile)
                {
                    Interlocked.Increment(ref foundCount);
                }
            };

            try
            {
                var cts = new CancellationTokenSource(TimeSpan.FromMinutes(2)); // 2分钟超时
                await _detector.ScanUrlsAsync(urls, concurrency, cts.Token);
            }
            catch (OperationCanceledException)
            {
                Console.WriteLine("测试超时");
            }

            stopwatch.Stop();

            var totalTime = stopwatch.ElapsedMilliseconds;
            var urlsPerSecond = completedCount > 0 ? (double)completedCount / (totalTime / 1000.0) : 0;

            Console.WriteLine(string.Format("  完成URL数量: {0}/{1}", completedCount, urls.Count));
            Console.WriteLine(string.Format("  发现备份文件: {0}", foundCount));
            Console.WriteLine(string.Format("  总耗时: {0:N0} ms", totalTime));
            Console.WriteLine(string.Format("  平均速度: {0:F2} URLs/秒", urlsPerSecond));
            Console.WriteLine(string.Format("  平均延迟: {0:F2} ms/URL", (completedCount > 0 ? totalTime / (double)completedCount : 0)));
        }

        private async Task ComparePerformanceModes(List<string> urls)
        {
            Console.WriteLine("=== 性能模式对比测试 ===");
            
            // 取前100个URL进行对比测试
            var testUrls = urls.GetRange(0, Math.Min(100, urls.Count));
            
            // 测试标准模式
            Console.WriteLine("测试标准扫描模式...");
            var standardResult = await MeasurePerformance(async () =>
            {
                await _detector.ScanUrlsAsync(testUrls, 20);
            });

            // 测试高性能模式
            Console.WriteLine("测试高性能扫描模式...");
            var highPerfResult = await MeasurePerformance(async () =>
            {
                await _detector.ScanUrlsHighPerformanceAsync(testUrls, 20);
            });

            // 输出对比结果
            Console.WriteLine();
            Console.WriteLine("性能对比结果:");
            Console.WriteLine(string.Format("标准模式耗时: {0:N0} ms", standardResult));
            Console.WriteLine(string.Format("高性能模式耗时: {0:N0} ms", highPerfResult));
            
            if (standardResult > 0)
            {
                var improvement = ((double)(standardResult - highPerfResult) / standardResult) * 100;
                Console.WriteLine(string.Format("性能提升: {0:F1}%", improvement));
            }
        }

        private async Task<long> MeasurePerformance(Func<Task> action)
        {
            var stopwatch = Stopwatch.StartNew();
            
            try
            {
                var cts = new CancellationTokenSource(TimeSpan.FromMinutes(1));
                await action();
            }
            catch (OperationCanceledException)
            {
                Console.WriteLine("操作超时");
            }
            
            stopwatch.Stop();
            return stopwatch.ElapsedMilliseconds;
        }

        /// <summary>
        /// 内存使用情况测试
        /// </summary>
        public void TestMemoryUsage()
        {
            Console.WriteLine("=== 内存使用测试 ===");
            
            var initialMemory = GC.GetTotalMemory(true);
            Console.WriteLine(string.Format("初始内存使用: {0:F2} MB", initialMemory / 1024 / 1024));

            // 生成大量URL
            var urls = new List<string>();
            foreach (var baseUrl in _testUrls)
            {
                var generatedUrls = _detector.GenerateBackupUrls(baseUrl);
                urls.AddRange(generatedUrls);
            }

            var afterGenerationMemory = GC.GetTotalMemory(false);
            Console.WriteLine(string.Format("URL生成后内存: {0:F2} MB", afterGenerationMemory / 1024 / 1024));
            Console.WriteLine(string.Format("URL生成内存增长: {0:F2} MB", (afterGenerationMemory - initialMemory) / 1024 / 1024));

            // 强制垃圾回收
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();

            var afterGCMemory = GC.GetTotalMemory(true);
            Console.WriteLine(string.Format("GC后内存: {0:F2} MB", afterGCMemory / 1024 / 1024));
            Console.WriteLine(string.Format("可回收内存: {0:F2} MB", (afterGenerationMemory - afterGCMemory) / 1024 / 1024));
        }

        public void Dispose()
        {
            if (_detector != null)
                _detector.Dispose();
        }
    }
}
