@echo off
echo ========================================
echo BackupFileScanner 新功能测试
echo ========================================
echo.

echo 测试内容:
echo 1. 超时和延时单位改为秒
echo 2. 新增随机X-Forwarded-For头选项
echo.

echo 启动程序进行手动测试...
echo.
echo 请在程序中验证以下功能:
echo - 超时时间显示为"超时(秒)"，默认值为10秒
echo - 延迟时间显示为"延迟(秒)"，默认值为0.1秒
echo - 新增"随机X-Forwarded-For头"复选框，默认不勾选
echo.

start "" "bin\Release\BackupFileScanner.exe"

echo 程序已启动，请手动测试新功能。
echo 测试完成后按任意键继续...
pause >nul

echo.
echo 功能说明:
echo ========================================
echo.
echo 1. 超时和延时单位修改:
echo    - 超时时间: 1-300秒 (原来是1000-60000毫秒)
echo    - 延迟时间: 0.1-10秒 (原来是100-5000毫秒)
echo    - 界面显示更直观，便于用户理解
echo.
echo 2. 随机X-Forwarded-For头:
echo    - 默认不勾选，保持原有行为
echo    - 勾选后会为每个请求添加随机的私有IP地址
echo    - 支持的IP范围:
echo      * ******** - **************
echo      * ********** - **************  
echo      * *********** - ***************
echo      * 127.0.0.1 - ***********
echo    - 可以帮助绕过某些基于IP的访问限制
echo.
echo 使用建议:
echo - 一般扫描: 超时10秒，延迟0.1秒
echo - 快速扫描: 超时5秒，延迟0秒
echo - 谨慎扫描: 超时30秒，延迟1秒
echo - 需要绕过IP限制时勾选随机X-Forwarded-For头
echo.
pause
