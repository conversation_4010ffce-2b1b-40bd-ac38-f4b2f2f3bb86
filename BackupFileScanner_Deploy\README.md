# 网站备份文件泄露检测工具

一个基于.NET Framework的GUI工具，用于检测网站是否存在备份文件泄露漏洞。

## 功能特性

### 核心功能
- ✅ **单个URL检测**：支持输入单个URL进行检测
- ✅ **批量URL检测**：支持从文件加载URL列表进行批量检测
- ✅ **多线程扫描**：支持配置并发线程数，提高扫描效率
- ✅ **速率控制**：支持设置请求间隔，避免对目标服务器造成过大压力
- ✅ **HTTP方法选择**：支持HEAD和GET两种请求方式，默认使用HEAD方法
- ✅ **字典文件支持**：
  - `dict/dir.dict` - 备份文件名字典（如www、web、backup等）
  - `dict/archiver.dict` - 备份文件后缀名字典（如.zip、.rar、.7z等）
  - `dict/UA.dict` - User-Agent字典，每次请求随机选择
- ✅ **自定义请求头**：支持添加自定义HTTP请求头
- ✅ **HTTPS支持**：解决HTTPS网站访问问题，支持忽略SSL证书错误

### 检测算法
工具使用以下算法判断备份文件是否真实存在：
1. HTTP状态码必须为200
2. 响应内容类型不能是`text/html`
3. 响应内容长度必须大于0
4. 优先识别常见的归档文件MIME类型
5. 对于未知类型，根据文件大小进行合理性判断（1KB-500MB）

### 界面功能
- 📊 **实时结果显示**：扫描结果实时显示在列表中
- 📝 **详细日志**：显示扫描过程的详细日志信息
- 📈 **进度跟踪**：显示扫描进度和统计信息
- 💾 **结果导出**：支持将扫描结果导出为CSV文件
- 🎨 **状态标识**：不同颜色标识不同的扫描结果

## 系统要求

- Windows 7/8/10/11
- .NET Framework 4.7.2 或更高版本
- 至少 50MB 可用磁盘空间

## 安装和使用

### 编译项目
1. 使用Visual Studio 2017或更高版本打开项目
2. 确保已安装.NET Framework 4.7.2 SDK
3. 生成解决方案

### 运行程序
1. 运行编译后的 `BackupFileScanner.exe`
2. 在"扫描目标"区域选择扫描模式：
   - **单个URL**：直接输入要检测的网站URL
   - **URL列表**：输入多个URL（每行一个）或从文件加载
3. 在"扫描设置"区域配置参数：
   - **请求方式**：选择HEAD或GET方法
   - **忽略SSL错误**：建议勾选以避免HTTPS证书问题
   - **超时时间**：设置HTTP请求超时时间（毫秒）
   - **线程数**：设置并发扫描线程数
   - **延迟**：设置请求间隔时间（毫秒）
   - **自定义头**：添加自定义HTTP请求头
4. 点击"开始扫描"按钮开始检测
5. 查看扫描结果和日志信息
6. 可以导出结果到CSV文件

### 字典文件配置

#### dir.dict（备份文件名）
```
www
web
backup
bak
old
temp
data
admin
...
```

#### archiver.dict（文件扩展名）
```
.zip
.rar
.7z
.tar.gz
.bak
.backup
...
```

#### UA.dict（User-Agent）
```
Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36...
Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36...
...
```

## 使用示例

### 单个网站检测
1. 选择"单个URL"
2. 输入：`https://example.com`
3. 工具会自动生成如下检测URL：
   - `https://example.com/www.zip`
   - `https://example.com/web.rar`
   - `https://example.com/backup.7z`
   - 等等...

### 批量网站检测
1. 选择"URL列表"
2. 输入多个URL或从文件加载：
   ```
   https://site1.com
   https://site2.com
   https://site3.com
   ```

### 自定义请求头
在自定义头字段中输入：
```
X-Forwarded-For: 127.0.0.1
X-Real-IP: 127.0.0.1
```

## 注意事项

⚠️ **重要提醒**：
- 本工具仅用于安全测试和漏洞检测
- 请确保您有权限对目标网站进行安全测试
- 请合理设置扫描速率，避免对目标服务器造成过大负载
- 建议在测试前先征得目标网站管理员的同意

## 技术特点

- **异步多线程**：使用async/await模式实现高效的并发扫描
- **内存优化**：合理管理HTTP连接和资源释放
- **错误处理**：完善的异常处理机制，确保程序稳定运行
- **用户体验**：友好的GUI界面，实时反馈扫描状态

## 更新日志

### v1.0.0
- 初始版本发布
- 实现基本的备份文件检测功能
- 支持单个和批量URL检测
- 实现多线程扫描和速率控制
- 添加字典文件支持
- 完善HTTPS支持和错误处理

## 许可证

本项目仅供学习和安全测试使用，请遵守相关法律法规。
