# 网站备份文件泄露检测工具 - 界面布局说明

## 界面布局结构

界面采用从上到下的合理布局，分为以下几个主要区域：

### 1. 顶部区域 - 配置区域
```
┌─────────────────────────────────────────────────────────────────────┐
│  扫描目标 (左侧)                    │  扫描设置 (右侧)                │
│  ┌─────────────────────────────┐    │  ┌─────────────────────────────┐ │
│  │ ○ 单个URL  ○ URL列表       │    │  │ ○ HEAD  ○ GET              │ │
│  │ 单个URL: [____________]     │    │  │ ☑ 忽略SSL错误               │ │
│  │ URL列表: [____________]     │    │  │ 超时: [10000] ms            │ │
│  │          [____________]     │    │  │ 线程数: [10]                │ │
│  │          [从文件加载]       │    │  │ 延迟: [100] ms              │ │
│  └─────────────────────────────┘    │  │ 自定义头: [X-Forwarded...] │ │
│                                     │  └─────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────┘
```

### 2. 中部区域 - 操作按钮
```
┌─────────────────────────────────────────────────────────────────────┐
│  [开始扫描] [停止扫描] [清空结果] [导出结果]                          │
└─────────────────────────────────────────────────────────────────────┘
```

### 3. 主体区域 - 扫描结果
```
┌─────────────────────────────────────────────────────────────────────┐
│  扫描结果                                                           │
│  ┌─────────────────────────────────────────────────────────────────┐ │
│  │ URL                │状态码│内容类型    │大小    │响应时间│检测结果│ │
│  │ https://...        │ 200  │application │1.2MB   │150ms   │发现备份│ │
│  │ https://...        │ 404  │text/html   │0B      │80ms    │正常    │ │
│  │ https://...        │ 200  │application │856KB   │200ms   │发现备份│ │
│  │ ...                │ ...  │...         │...     │...     │...     │ │
│  └─────────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────┘
```

### 4. 底部区域 - 日志信息
```
┌─────────────────────────────────────────────────────────────────────┐
│  日志信息                                                           │
│  ┌─────────────────────────────────────────────────────────────────┐ │
│  │ [19:30:15] 字典加载完成: 目录名 80 个, 扩展名 40 个, UA 20 个     │ │
│  │ [19:30:20] 开始扫描，共 1200 个URL                              │ │
│  │ [19:30:25] 已扫描: 150/1200                                     │ │
│  │ [19:30:30] 发现备份文件: https://example.com/backup.zip         │ │
│  └─────────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────┘
```

### 5. 最底部 - 状态栏
```
┌─────────────────────────────────────────────────────────────────────┐
│  就绪                                    ████████████░░░░░░░░ 60%     │
└─────────────────────────────────────────────────────────────────────┘
```

## 布局优势

### 1. 逻辑清晰
- **配置区域**：用户首先配置扫描参数
- **操作区域**：配置完成后执行操作
- **结果区域**：查看扫描结果
- **日志区域**：监控扫描过程
- **状态区域**：显示当前状态

### 2. 操作流程顺畅
```
配置扫描参数 → 点击开始扫描 → 查看扫描结果 → 监控日志信息 → 导出结果
     ↑              ↓              ↓              ↓           ↓
   顶部区域      中部按钮区域    主体结果区域    底部日志区域   按钮操作
```

### 3. 视觉层次分明
- **顶部**：输入和配置（用户关注度最高）
- **中部**：操作按钮（操作触发区域）
- **主体**：结果展示（信息量最大的区域）
- **底部**：辅助信息（日志和状态）

### 4. 空间利用合理
- **扫描结果区域**：占用最大空间，便于查看大量结果
- **配置区域**：适中大小，包含所有必要配置项
- **日志区域**：适当大小，显示关键日志信息
- **按钮区域**：紧凑布局，便于快速操作

## 界面尺寸规格

- **窗体总大小**：792 x 572 像素
- **扫描目标区域**：400 x 150 像素
- **扫描设置区域**：350 x 150 像素
- **操作按钮区域**：768 x 30 像素
- **扫描结果区域**：768 x 200 像素
- **日志信息区域**：768 x 100 像素
- **状态栏区域**：792 x 22 像素

## 颜色标识

### 扫描结果颜色编码
- **绿色背景**：发现备份文件（重要发现）
- **红色背景**：请求出错（需要关注）
- **白色背景**：正常响应（无发现）

### 按钮状态
- **启用状态**：正常颜色，可点击
- **禁用状态**：灰色，不可点击
- **默认按钮**：蓝色边框（开始扫描）

## 用户体验优化

1. **响应式布局**：各区域大小合理分配
2. **操作便捷**：按钮位置符合操作流程
3. **信息清晰**：结果和日志分离显示
4. **状态反馈**：实时进度和状态显示
5. **视觉引导**：颜色编码帮助快速识别

这种布局设计确保了用户能够按照自然的操作流程使用工具，同时最大化了信息展示效率。
