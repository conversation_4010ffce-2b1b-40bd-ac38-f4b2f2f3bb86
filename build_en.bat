@echo off
echo Building BackupFileScanner...
echo.

REM Create output directory
if not exist "bin" mkdir "bin"
if not exist "bin\Release" mkdir "bin\Release"

echo Compiling...
"C:\Windows\Microsoft.NET\Framework64\v4.0.30319\csc.exe" /target:winexe /out:bin\Release\BackupFileScanner.exe /reference:System.dll /reference:System.Core.dll /reference:System.Data.dll /reference:System.Drawing.dll /reference:System.Net.Http.dll /reference:System.Windows.Forms.dll /reference:System.Xml.dll /reference:System.Configuration.dll Program.cs MainForm.cs MainForm.Designer.cs BackupFileDetector.cs ScanResult.cs Properties\AssemblyInfo.cs Properties\Resources.Designer.cs Properties\Settings.Designer.cs

if %ERRORLEVEL% EQU 0 (
    echo.
    echo Build successful!
    echo Executable location: bin\Release\BackupFileScanner.exe
    echo.
    echo Copying config and dictionary files...
    copy "App.config" "bin\Release\BackupFileScanner.exe.config" >nul 2>&1
    if not exist "bin\Release\dict" mkdir "bin\Release\dict"
    copy "dict\*.dict" "bin\Release\dict\" >nul 2>&1
    echo Files copied successfully!
    echo.
    echo Build complete! You can run bin\Release\BackupFileScanner.exe
) else (
    echo.
    echo Build failed! Please check error messages above.
)

pause
