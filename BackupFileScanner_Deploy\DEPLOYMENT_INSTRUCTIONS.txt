BackupFileScanner Deployment Instructions
=========================================

System Requirements:
- Windows 7/8/10/11
- .NET Framework 4.7.2 or higher

File Description:
- BackupFileScanner.exe        : Main program
- BackupFileScanner.exe.config : Configuration file (REQUIRED)
- dict/                        : Dictionary folder (REQUIRED)
  - dir.dict                   : Directory name dictionary
  - archiver.dict              : Archive file extension dictionary
  - UA.dict                    : User-Agent dictionary

Usage:
1. Copy the entire folder to target computer
2. Ensure .NET Framework 4.7.2+ is installed on target computer
3. Double-click BackupFileScanner.exe to run

Important Notes:
- ALL files must be kept in the same directory
- Do NOT delete or modify the .config file
- Dictionary files in dict folder are essential

For more information, see README.md
