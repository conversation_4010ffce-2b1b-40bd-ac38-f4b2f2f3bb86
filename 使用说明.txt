网站备份文件泄露检测工具 v1.0 使用说明
===========================================

一、工具简介
-----------
本工具是一个基于.NET Framework的GUI应用程序，专门用于检测网站是否存在备份文件泄露漏洞。
工具会自动生成常见的备份文件URL并进行检测，帮助安全人员发现潜在的信息泄露风险。

二、主要功能
-----------
✓ 单个URL检测和批量URL检测
✓ 多线程并发扫描，支持速率控制
✓ 支持HEAD和GET两种HTTP请求方式
✓ 智能备份文件识别算法
✓ 自定义HTTP请求头支持
✓ 完善的HTTPS支持
✓ 实时结果显示和日志记录
✓ 扫描结果导出功能

三、安装要求
-----------
- Windows 7/8/10/11 操作系统
- .NET Framework 4.7.2 或更高版本
- 至少 50MB 可用磁盘空间

四、使用方法
-----------

1. 启动程序
   双击运行 BackupFileScanner.exe

2. 选择扫描目标
   - 单个URL：直接输入要检测的网站地址
   - URL列表：输入多个URL（每行一个）或点击"从文件加载"

3. 配置扫描参数
   - 请求方式：建议使用HEAD方法（速度更快）
   - 忽略SSL错误：建议勾选（避免HTTPS证书问题）
   - 超时时间：默认10秒，可根据网络情况调整
   - 线程数：默认10个，可根据需要调整
   - 延迟：请求间隔时间，避免对目标服务器造成压力
   - 自定义头：可添加如 X-Forwarded-For: 127.0.0.1

4. 开始扫描
   点击"开始扫描"按钮，工具会自动生成备份文件URL并进行检测

5. 查看结果
   - 绿色行：发现备份文件
   - 红色行：请求出错
   - 白色行：正常响应（未发现备份文件）

6. 导出结果
   点击"导出结果"可将扫描结果保存为CSV文件

五、检测原理
-----------
工具会根据输入的URL自动生成可能的备份文件地址，例如：
- https://example.com/www.zip
- https://example.com/backup.rar
- https://example.com/web.tar.gz
- 等等...

判断备份文件的算法：
1. HTTP状态码必须为200
2. 响应内容类型不能是text/html
3. 响应内容长度必须大于0
4. 优先识别常见归档文件MIME类型
5. 对未知类型根据文件大小判断（1KB-500MB）

六、字典文件说明
--------------
工具使用三个字典文件：

1. dict/dir.dict - 备份文件名字典
   包含常见的备份文件名，如：www, web, backup, bak, old 等

2. dict/archiver.dict - 文件扩展名字典
   包含常见的压缩文件扩展名，如：.zip, .rar, .7z, .tar.gz 等

3. dict/UA.dict - User-Agent字典
   包含常见的浏览器User-Agent，每次请求随机选择

可以根据需要编辑这些字典文件来自定义检测范围。

七、注意事项
-----------
⚠️ 重要提醒：
- 本工具仅用于授权的安全测试
- 请确保您有权限对目标网站进行安全测试
- 合理设置扫描速率，避免对目标服务器造成过大负载
- 建议在测试前征得目标网站管理员同意
- 遵守相关法律法规和道德规范

八、故障排除
-----------
1. 程序无法启动
   - 检查是否安装了.NET Framework 4.7.2或更高版本
   - 确保dict目录和字典文件存在

2. HTTPS网站无法访问
   - 确保勾选了"忽略SSL错误"选项
   - 检查网络连接和防火墙设置

3. 扫描速度慢
   - 适当增加线程数
   - 减少请求延迟时间
   - 使用HEAD方法而不是GET方法

4. 误报问题
   - 检测算法可能需要根据具体情况调整
   - 可以通过查看响应详情来确认结果

九、技术支持
-----------
如有问题或建议，请查看README.md文件获取更多技术信息。

十、版本信息
-----------
当前版本：v1.0.0
发布日期：2024年
开发环境：.NET Framework 4.7.2
编程语言：C#

===========================================
感谢使用网站备份文件泄露检测工具！
