@echo off
echo 正在编译网站备份文件泄露检测工具...
echo.

REM 查找MSBuild路径
set MSBUILD_PATH=""
if exist "%ProgramFiles(x86)%\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="%ProgramFiles(x86)%\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe"
) else if exist "%ProgramFiles(x86)%\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="%ProgramFiles(x86)%\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe"
) else if exist "%ProgramFiles(x86)%\Microsoft Visual Studio\2017\Professional\MSBuild\15.0\Bin\MSBuild.exe" (
    set MSBUILD_PATH="%ProgramFiles(x86)%\Microsoft Visual Studio\2017\Professional\MSBuild\15.0\Bin\MSBuild.exe"
) else if exist "%ProgramFiles(x86)%\Microsoft Visual Studio\2017\Community\MSBuild\15.0\Bin\MSBuild.exe" (
    set MSBUILD_PATH="%ProgramFiles(x86)%\Microsoft Visual Studio\2017\Community\MSBuild\15.0\Bin\MSBuild.exe"
) else if exist "%ProgramFiles(x86)%\MSBuild\14.0\Bin\MSBuild.exe" (
    set MSBUILD_PATH="%ProgramFiles(x86)%\MSBuild\14.0\Bin\MSBuild.exe"
) else (
    echo 错误: 未找到MSBuild，请确保已安装Visual Studio或Build Tools
    pause
    exit /b 1
)

echo 使用MSBuild路径: %MSBUILD_PATH%
echo.

REM 编译项目
%MSBUILD_PATH% BackupFileScanner.csproj /p:Configuration=Release /p:Platform="Any CPU" /verbosity:minimal

if %ERRORLEVEL% EQU 0 (
    echo.
    echo 编译成功！
    echo 可执行文件位置: bin\Release\BackupFileScanner.exe
    echo.
    echo 正在复制字典文件到输出目录...
    if not exist "bin\Release\dict" mkdir "bin\Release\dict"
    copy "dict\*.dict" "bin\Release\dict\" >nul
    echo 字典文件复制完成！
    echo.
    echo 按任意键运行程序...
    pause >nul
    start "" "bin\Release\BackupFileScanner.exe"
) else (
    echo.
    echo 编译失败！请检查错误信息。
    pause
)
