using System;

namespace BackupFileScanner
{
    /// <summary>
    /// 扫描结果类
    /// </summary>
    public class ScanResult
    {
        /// <summary>
        /// 目标URL
        /// </summary>
        public string Url { get; set; }

        /// <summary>
        /// HTTP状态码
        /// </summary>
        public int StatusCode { get; set; }

        /// <summary>
        /// 响应内容类型
        /// </summary>
        public string ContentType { get; set; }

        /// <summary>
        /// 响应内容长度
        /// </summary>
        public long ContentLength { get; set; }

        /// <summary>
        /// 是否检测到备份文件
        /// </summary>
        public bool IsBackupFile { get; set; }

        /// <summary>
        /// 响应时间（毫秒）
        /// </summary>
        public long ResponseTime { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 扫描时间
        /// </summary>
        public DateTime ScanTime { get; set; }

        /// <summary>
        /// 使用的HTTP方法
        /// </summary>
        public string HttpMethod { get; set; }

        /// <summary>
        /// 服务器响应头信息
        /// </summary>
        public string ServerHeader { get; set; }

        public ScanResult()
        {
            ScanTime = DateTime.Now;
        }

        public override string ToString()
        {
            if (IsBackupFile)
            {
                return string.Format("[发现备份文件] {0} - 状态码: {1}, 类型: {2}, 大小: {3} bytes", Url, StatusCode, ContentType, ContentLength);
            }
            else if (!string.IsNullOrEmpty(ErrorMessage))
            {
                return string.Format("[错误] {0} - {1}", Url, ErrorMessage);
            }
            else
            {
                return string.Format("[正常] {0} - 状态码: {1}", Url, StatusCode);
            }
        }
    }
}
