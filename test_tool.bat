@echo off
echo Testing BackupFileScanner Tool...
echo.

REM Check if executable exists
if not exist "bin\Release\BackupFileScanner.exe" (
    echo Error: BackupFileScanner.exe not found!
    echo Please run build_en.bat first to compile the project.
    pause
    exit /b 1
)

REM Check if dictionary files exist
if not exist "bin\Release\dict\dir.dict" (
    echo Error: Dictionary files not found!
    echo Please ensure dict folder and files are copied to bin\Release\
    pause
    exit /b 1
)

echo All required files found!
echo.
echo Dictionary files status:
dir "bin\Release\dict\*.dict" /b
echo.

echo Starting BackupFileScanner...
echo.
echo Note: This will open the GUI application.
echo You can test it with sample URLs like:
echo - https://httpbin.org
echo - https://www.example.com
echo.

start "" "bin\Release\BackupFileScanner.exe"

echo Tool launched successfully!
echo Check the GUI window to use the application.
pause
