using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Diagnostics;

namespace BackupFileScanner
{
    /// <summary>
    /// 备份文件检测器核心类
    /// </summary>
    public class BackupFileDetector
    {
        private readonly HttpClient _httpClient;
        private readonly Random _random;
        private List<string> _directoryNames;
        private List<string> _archiveExtensions;
        private List<string> _userAgents;
        private Dictionary<string, string> _customHeaders;

        // 静态常量定义，避免重复创建
        private static readonly HashSet<string> CommonSecondLevelDomains = new HashSet<string>
        {
            "com.cn", "net.cn", "org.cn", "gov.cn", "edu.cn",
            "com.hk", "net.hk", "org.hk", "gov.hk", "edu.hk",
            "co.uk", "org.uk", "ac.uk", "gov.uk",
            "co.jp", "ne.jp", "or.jp", "ac.jp", "go.jp",
            "com.au", "net.au", "org.au", "edu.au", "gov.au"
        };

        private static readonly HashSet<string> TopLevelDomains = new HashSet<string>
        {
            "com", "net", "org", "edu", "gov", "mil", "int"
        };

        private static readonly string[] NumberSuffixes = { "1", "01", "2", "02" };

        // 静态MIME类型数组，避免每次重新创建
        private static readonly string[] ArchiveContentTypes = new[]
        {
            // ZIP格式
            "application/zip",
            "application/x-zip-compressed",
            "application/x-zip",

            // RAR格式
            "application/x-rar-compressed",
            "application/x-rar",
            "application/vnd.rar",

            // 7Z格式
            "application/x-7z-compressed",
            "application/x-7z",

            // TAR格式
            "application/x-tar",
            "application/tar",

            // GZIP格式
            "application/gzip",
            "application/x-gzip",
            "application/x-gunzip",

            // BZIP2格式
            "application/x-bzip2",
            "application/x-bzip",
            "application/bzip2",

            // XZ格式
            "application/x-xz",
            "application/xz",

            // LZMA格式
            "application/x-lzma",
            "application/lzma",

            // 其他压缩格式
            "application/x-compressed",
            "application/x-compress",
            "application/x-ace-compressed",
            "application/x-arj",
            "application/x-cab",
            "application/vnd.ms-cab-compressed",
            "application/x-lzh-compressed",
            "application/x-lha",
            "application/x-stuffit",
            "application/x-sit",
            "application/x-zoo",

            // 数据库备份文件
            "application/x-sql",
            "application/sql",
            "text/x-sql",
            "text/sql",
            "application/x-mysql",
            "application/x-postgresql",
            "application/x-sqlite3",
            "application/x-sqlite",
            "application/vnd.sqlite3",
            "application/x-dbf",
            "application/x-msaccess",
            "application/vnd.ms-access",
            "application/x-mdb",
            "application/mdb",
            "application/x-db",
            "application/x-database",
            "application/x-dump",
            "application/x-backup",
            "application/x-bak",

            // 通用二进制文件
            "application/octet-stream"
        };

        public int Timeout { get; set; }
        public int DelayBetweenRequests { get; set; }
        public string HttpMethod { get; set; }
        public bool IgnoreSSLErrors { get; set; }

        public event EventHandler<ScanResult> ScanCompleted;
        public event EventHandler<string> LogMessage;

        public BackupFileDetector()
        {
            // 设置默认值
            Timeout = 10000; // 10秒超时
            DelayBetweenRequests = 100; // 请求间隔100ms
            HttpMethod = "HEAD"; // 默认使用HEAD方法
            IgnoreSSLErrors = true; // 忽略SSL错误

            _random = new Random();
            _customHeaders = new Dictionary<string, string>();

            // 配置HttpClientHandler以处理HTTPS问题
            var handler = new HttpClientHandler()
            {
                ServerCertificateCustomValidationCallback = (sender, cert, chain, sslPolicyErrors) => this.IgnoreSSLErrors
            };

            _httpClient = new HttpClient(handler);
            UpdateHttpClientTimeout();

            LoadDictionaries();
        }

        /// <summary>
        /// 更新HttpClient超时设置
        /// </summary>
        private void UpdateHttpClientTimeout()
        {
            _httpClient.Timeout = TimeSpan.FromMilliseconds(Timeout);
        }

        /// <summary>
        /// 加载字典文件
        /// </summary>
        private void LoadDictionaries()
        {
            try
            {
                // 加载目录名字典
                if (File.Exists("dict/dir.dict"))
                {
                    _directoryNames = File.ReadAllLines("dict/dir.dict", Encoding.UTF8)
                        .Where(line => !string.IsNullOrWhiteSpace(line))
                        .Select(line => line.Trim())
                        .ToList();
                }
                else
                {
                    _directoryNames = new List<string> { "www", "web", "backup", "bak", "old" };
                }

                // 加载归档文件扩展名字典
                if (File.Exists("dict/archiver.dict"))
                {
                    _archiveExtensions = File.ReadAllLines("dict/archiver.dict", Encoding.UTF8)
                        .Where(line => !string.IsNullOrWhiteSpace(line))
                        .Select(line => line.Trim())
                        .ToList();
                }
                else
                {
                    _archiveExtensions = new List<string> { ".zip", ".rar", ".7z", ".tar.gz", ".bak" };
                }

                // 加载User-Agent字典
                if (File.Exists("dict/UA.dict"))
                {
                    _userAgents = File.ReadAllLines("dict/UA.dict", Encoding.UTF8)
                        .Where(line => !string.IsNullOrWhiteSpace(line))
                        .Select(line => line.Trim())
                        .ToList();
                }
                else
                {
                    _userAgents = new List<string> 
                    { 
                        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36" 
                    };
                }

                OnLogMessage(string.Format("字典加载完成: 目录名 {0} 个, 扩展名 {1} 个, User-Agent {2} 个", _directoryNames.Count, _archiveExtensions.Count, _userAgents.Count));
            }
            catch (Exception ex)
            {
                OnLogMessage(string.Format("加载字典文件时出错: {0}", ex.Message));
            }
        }

        /// <summary>
        /// 设置自定义请求头
        /// </summary>
        public void SetCustomHeaders(Dictionary<string, string> headers)
        {
            _customHeaders = headers ?? new Dictionary<string, string>();
        }

        /// <summary>
        /// 提取主域名部分
        /// </summary>
        private string ExtractMainDomain(string[] domainParts)
        {
            if (domainParts == null || domainParts.Length < 2)
                return "";

            // 重构域名以检查二级域名
            if (domainParts.Length >= 3)
            {
                var lastTwoParts = domainParts[domainParts.Length - 2] + "." + domainParts[domainParts.Length - 1];

                // 如果是二级域名（如 com.cn, com.hk）
                if (CommonSecondLevelDomains.Contains(lastTwoParts))
                {
                    if (domainParts.Length >= 3)
                    {
                        // 对于 www.test.com.cn -> test
                        // 对于 sub.test.com.cn -> test
                        var candidateIndex = domainParts.Length - 3;
                        if (candidateIndex >= 0 && domainParts[0] == "www" && domainParts.Length == 4)
                        {
                            return domainParts[1]; // www.test.com.cn -> test
                        }
                        else if (candidateIndex >= 0)
                        {
                            return domainParts[candidateIndex]; // sub.test.com.cn -> test
                        }
                    }
                }
            }

            // 处理普通域名
            if (domainParts.Length == 2)
            {
                return domainParts[0]; // baidu.com -> baidu
            }
            else if (domainParts.Length == 3 && domainParts[0] == "www")
            {
                return domainParts[1]; // www.baidu.com -> baidu
            }
            else if (domainParts.Length >= 3)
            {
                // 对于其他多级域名，取倒数第二个部分（但要避免取到顶级域名）
                var candidate = domainParts[domainParts.Length - 2];
                // 避免返回常见的顶级域名
                if (!TopLevelDomains.Contains(candidate))
                {
                    return candidate;
                }
                // 如果倒数第二个是顶级域名，尝试倒数第三个
                else if (domainParts.Length >= 4)
                {
                    return domainParts[domainParts.Length - 3];
                }
            }

            return "";
        }

        /// <summary>
        /// 提取子域名关键字
        /// </summary>
        private List<string> ExtractSubdomainKeywords(string[] domainParts)
        {
            var keywords = new List<string>();

            if (domainParts == null || domainParts.Length < 3)
                return keywords;

            // 确定域名的结构
            var domainStartIndex = domainParts.Length - 2; // 默认从倒数第二个开始是域名部分

            if (domainParts.Length >= 3)
            {
                var lastTwoParts = domainParts[domainParts.Length - 2] + "." + domainParts[domainParts.Length - 1];
                if (CommonSecondLevelDomains.Contains(lastTwoParts))
                {
                    domainStartIndex = domainParts.Length - 3; // 对于二级域名，从倒数第三个开始是域名部分
                }
            }

            // 提取子域名关键字（排除www和域名部分）
            for (int i = 0; i < domainStartIndex; i++)
            {
                var part = domainParts[i];
                if (!string.IsNullOrEmpty(part) && part != "www")
                {
                    keywords.Add(part);
                }
            }

            return keywords;
        }

        /// <summary>
        /// 根据域名生成可能的备份文件名
        /// </summary>
        private List<string> GenerateDomainBasedNames(string hostname)
        {
            var names = new List<string>();

            if (string.IsNullOrEmpty(hostname))
                return names;

            // 移除端口号并转换为小写
            var host = hostname.Split(':')[0].ToLower();
            var parts = host.Split('.');

            // 1. 完整域名 (www.baidu.com)
            names.Add(host);

            // 2. 移除点号的域名 (wwwbaiducom)
            names.Add(host.Replace(".", ""));

            // 3. 去掉www后的域名
            if (parts.Length >= 2 && parts[0] == "www")
            {
                var domainWithoutWww = string.Join(".", parts.Skip(1).ToArray());
                names.Add(domainWithoutWww); // baidu.com, test.google.com
            }

            // 4. 多级子域名关键字提取
            if (parts.Length >= 3)
            {
                var subdomainParts = ExtractSubdomainKeywords(parts);
                foreach (var subdomain in subdomainParts)
                {
                    names.Add(subdomain); // api, dbadmin, t3
                    names.Add(subdomain.ToUpper()); // API, DBADMIN, T3
                }
            }

            // 5. 提取主域名部分并准备后缀生成的基础名称
            var mainDomain = "";
            if (parts.Length >= 2)
            {
                mainDomain = ExtractMainDomain(parts);

                if (!string.IsNullOrEmpty(mainDomain))
                {
                    names.Add(mainDomain); // baidu, test
                    names.Add(mainDomain.ToUpper()); // BAIDU, TEST
                }
            }

            // 6. 添加年份、数字和年月后缀
            var currentYear = DateTime.Now.Year;
            var baseNames = new List<string> { host };
            if (!string.IsNullOrEmpty(mainDomain))
            {
                baseNames.Add(mainDomain);
            }

            foreach (var baseName in baseNames)
            {
                // 年份后缀：当前年份到往前5年
                for (int i = 0; i <= 5; i++)
                {
                    var year = currentYear - i;
                    names.Add(baseName + year); // baidu2025
                    names.Add(baseName + "_" + year); // baidu_2025
                }

                // 数字后缀
                foreach (var suffix in NumberSuffixes)
                {
                    names.Add(baseName + suffix); // baidu1
                    names.Add(baseName + "_" + suffix); // baidu_1
                }

                // 年月后缀：当前年份和前一年的所有月份
                for (int year = currentYear - 1; year <= currentYear; year++)
                {
                    for (int month = 1; month <= 12; month++)
                    {
                        var yearMonth = year.ToString() + month.ToString("D2");
                        names.Add(baseName + yearMonth); // baidu202401
                        names.Add(baseName + "_" + yearMonth); // baidu_202401
                    }
                }
            }

            // 5. 移除重复项
            return names.Distinct().ToList();
        }

        /// <summary>
        /// 检查主机名是否为IP地址
        /// </summary>
        private bool IsIPAddress(string hostname)
        {
            if (string.IsNullOrEmpty(hostname))
                return false;

            // 移除端口号
            var host = hostname.Split(':')[0];

            // 尝试解析为IP地址
            System.Net.IPAddress ipAddress;
            return System.Net.IPAddress.TryParse(host, out ipAddress);
        }

        /// <summary>
        /// 生成备份文件URL列表
        /// </summary>
        public List<string> GenerateBackupUrls(string baseUrl)
        {
            var urls = new List<string>();

            try
            {
                var uri = new Uri(baseUrl);
                var baseUrlWithoutPath = uri.Scheme + "://" + uri.Host;
                if (uri.Port != 80 && uri.Port != 443 && uri.Port != -1)
                {
                    baseUrlWithoutPath += ":" + uri.Port;
                }

                // 1. 生成基于字典的备份文件URL
                foreach (var dirName in _directoryNames)
                {
                    foreach (var extension in _archiveExtensions)
                    {
                        urls.Add(baseUrlWithoutPath + "/" + dirName + extension);
                    }
                }

                // 2. 生成基于域名的备份文件URL（仅当主机名不是IP地址时）
                var domainBasedCount = 0;
                if (!IsIPAddress(uri.Host))
                {
                    var domainBasedNames = GenerateDomainBasedNames(uri.Host);
                    foreach (var domainName in domainBasedNames)
                    {
                        foreach (var extension in _archiveExtensions)
                        {
                            urls.Add(baseUrlWithoutPath + "/" + domainName + extension);
                        }
                    }
                    domainBasedCount = domainBasedNames.Count * _archiveExtensions.Count;
                    OnLogMessage(string.Format("生成备份URL: 字典文件 {0} 个, 域名相关 {1} 个, 总计 {2} 个",
                        _directoryNames.Count * _archiveExtensions.Count,
                        domainBasedCount,
                        urls.Count));
                }
                else
                {
                    OnLogMessage(string.Format("检测到IP地址 {0}，跳过域名相关字典生成。生成备份URL: 字典文件 {1} 个",
                        uri.Host,
                        _directoryNames.Count * _archiveExtensions.Count));
                }
            }
            catch (Exception ex)
            {
                OnLogMessage("生成备份URL时出错: " + ex.Message);
            }

            return urls;
        }

        /// <summary>
        /// 扫描单个URL
        /// </summary>
        public async Task<ScanResult> ScanUrlAsync(string url, CancellationToken cancellationToken = default(CancellationToken))
        {
            var result = new ScanResult
            {
                Url = url,
                HttpMethod = this.HttpMethod
            };

            var stopwatch = Stopwatch.StartNew();

            try
            {
                // 创建HTTP请求消息
                var httpMethod = HttpMethod.ToUpper() == "HEAD" ? System.Net.Http.HttpMethod.Head : System.Net.Http.HttpMethod.Get;
                using (var request = new HttpRequestMessage(httpMethod, url))
                {
                    // 设置随机User-Agent
                    var userAgent = _userAgents[_random.Next(_userAgents.Count)];
                    request.Headers.Add("User-Agent", userAgent);

                    // 添加自定义请求头
                    foreach (var header in _customHeaders)
                    {
                        try
                        {
                            request.Headers.Add(header.Key, header.Value);
                        }
                        catch (Exception ex)
                        {
                            OnLogMessage("添加自定义请求头失败 " + header.Key + ": " + ex.Message);
                        }
                    }

                    // 对于GET请求，添加Range头只获取前1KB数据进行快速检测
                    if (httpMethod == System.Net.Http.HttpMethod.Get)
                    {
                        request.Headers.Add("Range", "bytes=0-1023");
                    }

                    // 发送请求
                    using (var response = await _httpClient.SendAsync(request, cancellationToken))
                    {
                        stopwatch.Stop();
                        result.ResponseTime = stopwatch.ElapsedMilliseconds;
                        result.StatusCode = (int)response.StatusCode;
                        result.ContentType = response.Content.Headers.ContentType != null ? response.Content.Headers.ContentType.MediaType : "";

                        // 对于206响应，优先显示实际文件大小
                        if (response.StatusCode == HttpStatusCode.PartialContent && response.Content.Headers.ContentRange != null)
                        {
                            result.ContentLength = response.Content.Headers.ContentRange.Length ?? (response.Content.Headers.ContentLength ?? 0);
                        }
                        else
                        {
                            result.ContentLength = response.Content.Headers.ContentLength ?? 0;
                        }

                        result.ServerHeader = response.Headers.Server != null ? response.Headers.Server.ToString() : "";

                        // 判断是否为备份文件
                        result.IsBackupFile = IsBackupFile(response);
                    }
                }
            }
            catch (TaskCanceledException)
            {
                stopwatch.Stop();
                result.ResponseTime = stopwatch.ElapsedMilliseconds;
                result.ErrorMessage = "请求超时";
            }
            catch (HttpRequestException ex)
            {
                stopwatch.Stop();
                result.ResponseTime = stopwatch.ElapsedMilliseconds;
                result.ErrorMessage = "HTTP请求错误: " + ex.Message;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                result.ResponseTime = stopwatch.ElapsedMilliseconds;
                result.ErrorMessage = "未知错误: " + ex.Message;
            }

            return result;
        }

        /// <summary>
        /// 判断响应是否为备份文件
        /// </summary>
        private bool IsBackupFile(HttpResponseMessage response)
        {
            // 状态码必须是200 (OK) 或 206 (Partial Content)
            if (response.StatusCode != HttpStatusCode.OK && response.StatusCode != HttpStatusCode.PartialContent)
                return false;

            var contentType = response.Content.Headers.ContentType != null && response.Content.Headers.ContentType.MediaType != null ? response.Content.Headers.ContentType.MediaType.ToLower() : "";
            var contentLength = response.Content.Headers.ContentLength ?? 0;

            // 对于206响应，尝试从Content-Range头获取实际文件大小
            var actualFileSize = contentLength;
            if (response.StatusCode == HttpStatusCode.PartialContent && response.Content.Headers.ContentRange != null)
            {
                actualFileSize = response.Content.Headers.ContentRange.Length ?? contentLength;
            }

            // 内容类型不应该是text/html
            if (contentType.Contains("text/html"))
                return false;

            // 内容长度应该大于0
            if (contentLength <= 0 && actualFileSize <= 0)
                return false;

            // 检查是否为已知的归档文件类型
            if (ArchiveContentTypes.Any(type => contentType.Contains(type)))
                return true;

            // 如果内容类型不明确，但文件大小合理（大于1KB且小于500MB），也认为可能是备份文件
            if (string.IsNullOrEmpty(contentType) || contentType.Contains("octet-stream"))
            {
                var sizeToCheck = actualFileSize > 0 ? actualFileSize : contentLength;
                return sizeToCheck > 1024 && sizeToCheck < 500 * 1024 * 1024;
            }

            return false;
        }

        /// <summary>
        /// 批量扫描URL列表
        /// </summary>
        public async Task ScanUrlsAsync(List<string> urls, int maxConcurrency = 10, CancellationToken cancellationToken = default(CancellationToken))
        {
            var semaphore = new SemaphoreSlim(maxConcurrency, maxConcurrency);
            var tasks = new List<Task>();

            foreach (var url in urls)
            {
                if (cancellationToken.IsCancellationRequested)
                    break;

                var task = Task.Run(async () =>
                {
                    await semaphore.WaitAsync(cancellationToken);
                    try
                    {
                        var result = await ScanUrlAsync(url, cancellationToken);
                        OnScanCompleted(result);

                        // 添加延迟以控制扫描速率
                        if (DelayBetweenRequests > 0)
                        {
                            await Task.Delay(DelayBetweenRequests, cancellationToken);
                        }
                    }
                    finally
                    {
                        semaphore.Release();
                    }
                }, cancellationToken);

                tasks.Add(task);
            }

            await Task.WhenAll(tasks);
        }

        /// <summary>
        /// 从文件加载URL列表
        /// </summary>
        public List<string> LoadUrlsFromFile(string filePath)
        {
            try
            {
                return File.ReadAllLines(filePath, Encoding.UTF8)
                    .Where(line => !string.IsNullOrWhiteSpace(line))
                    .Select(line => line.Trim())
                    .Where(line => Uri.IsWellFormedUriString(line, UriKind.Absolute))
                    .ToList();
            }
            catch (Exception ex)
            {
                OnLogMessage("加载URL文件时出错: " + ex.Message);
                return new List<string>();
            }
        }

        protected virtual void OnScanCompleted(ScanResult result)
        {
            if (ScanCompleted != null)
                ScanCompleted.Invoke(this, result);
        }

        protected virtual void OnLogMessage(string message)
        {
            if (LogMessage != null)
                LogMessage.Invoke(this, string.Format("[{0:HH:mm:ss}] {1}", DateTime.Now, message));
        }

        public void Dispose()
        {
            if (_httpClient != null)
                _httpClient.Dispose();
        }
    }
}
