@echo off
echo 正在编译网站备份文件泄露检测工具...
echo.

REM 创建输出目录
if not exist "bin" mkdir "bin"
if not exist "bin\Release" mkdir "bin\Release"

echo 编译中...
cmd /c "C:\Windows\Microsoft.NET\Framework64\v4.0.30319\csc.exe" /target:winexe /out:bin\Release\BackupFileScanner.exe /reference:System.dll /reference:System.Core.dll /reference:System.Data.dll /reference:System.Drawing.dll /reference:System.Net.Http.dll /reference:System.Windows.Forms.dll /reference:System.Xml.dll /reference:System.Configuration.dll Program.cs MainForm.cs MainForm.Designer.cs BackupFileDetector.cs ScanResult.cs Properties\AssemblyInfo.cs Properties\Resources.Designer.cs Properties\Settings.Designer.cs

if %ERRORLEVEL% EQU 0 (
    echo.
    echo 编译成功！
    echo 可执行文件位置: bin\Release\BackupFileScanner.exe
    echo.
    echo 正在复制配置文件和字典文件...
    copy "App.config" "bin\Release\BackupFileScanner.exe.config" >nul 2>&1
    if not exist "bin\Release\dict" mkdir "bin\Release\dict"
    copy "dict\*.dict" "bin\Release\dict\" >nul 2>&1
    echo 文件复制完成！
    echo.
    echo 编译完成！可以运行 bin\Release\BackupFileScanner.exe
) else (
    echo.
    echo 编译失败！请检查错误信息。
)

pause
