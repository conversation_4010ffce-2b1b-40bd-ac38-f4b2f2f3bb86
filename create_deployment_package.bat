@echo off
echo ========================================
echo BackupFileScanner 部署包创建工具
echo ========================================
echo.

set DEPLOY_DIR=BackupFileScanner_Deploy
set SOURCE_DIR=bin\Release

echo 正在创建部署包...
echo.

REM 删除旧的部署目录
if exist "%DEPLOY_DIR%" (
    echo 删除旧的部署目录...
    rmdir /s /q "%DEPLOY_DIR%"
)

REM 创建部署目录
mkdir "%DEPLOY_DIR%"

REM 检查源文件是否存在
if not exist "%SOURCE_DIR%\BackupFileScanner.exe" (
    echo [错误] 找不到 BackupFileScanner.exe
    echo 请先运行 build_en.bat 编译程序
    pause
    exit /b 1
)

echo 复制主程序文件...
copy "%SOURCE_DIR%\BackupFileScanner.exe" "%DEPLOY_DIR%\" >nul

echo 复制配置文件...
copy "%SOURCE_DIR%\BackupFileScanner.exe.config" "%DEPLOY_DIR%\" >nul

echo 复制字典文件...
if not exist "%DEPLOY_DIR%\dict" mkdir "%DEPLOY_DIR%\dict"
copy "%SOURCE_DIR%\dict\*.dict" "%DEPLOY_DIR%\dict\" >nul

echo 复制文档文件...
copy "README.md" "%DEPLOY_DIR%\" >nul 2>&1
copy "使用说明.txt" "%DEPLOY_DIR%\" >nul 2>&1
copy "PERFORMANCE_OPTIMIZATION_REPORT.md" "%DEPLOY_DIR%\" >nul 2>&1

REM 创建部署说明文件
echo 创建部署说明...
(
echo BackupFileScanner 部署说明
echo ============================
echo.
echo 系统要求:
echo - Windows 7/8/10/11
echo - .NET Framework 4.7.2 或更高版本
echo.
echo 文件说明:
echo - BackupFileScanner.exe        : 主程序
echo - BackupFileScanner.exe.config : 配置文件 ^(必需^)
echo - dict/                        : 字典文件夹 ^(必需^)
echo   - dir.dict                   : 目录名字典
echo   - archiver.dict              : 压缩文件扩展名字典
echo   - UA.dict                    : User-Agent字典
echo.
echo 使用方法:
echo 1. 将整个文件夹复制到目标电脑
echo 2. 确保目标电脑已安装 .NET Framework 4.7.2+
echo 3. 双击运行 BackupFileScanner.exe
echo.
echo 注意事项:
echo - 所有文件都必须保持在同一目录下
echo - 不要删除或修改 .config 文件
echo - dict 文件夹中的字典文件不可缺少
echo.
echo 如有问题，请参考 README.md 或使用说明.txt
) > "%DEPLOY_DIR%\部署说明.txt"

REM 显示部署包内容
echo.
echo ========================================
echo 部署包创建完成！
echo ========================================
echo.
echo 部署包位置: %DEPLOY_DIR%\
echo.
echo 包含文件:
dir "%DEPLOY_DIR%" /b
echo.
echo 字典文件:
dir "%DEPLOY_DIR%\dict" /b
echo.
echo 部署包大小:
dir "%DEPLOY_DIR%" /s
echo.
echo 现在可以将 %DEPLOY_DIR% 文件夹整个复制到新电脑上使用。
echo.
pause
