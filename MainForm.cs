using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace BackupFileScanner
{
    public partial class MainForm : Form
    {
        private BackupFileDetector _detector;
        private CancellationTokenSource _cancellationTokenSource;
        private bool _isScanning = false;
        private int _totalUrls = 0;
        private int _scannedUrls = 0;
        private int _foundBackups = 0;

        public MainForm()
        {
            InitializeComponent();
            InitializeDetector();
            UpdateControlStates();
        }

        private void InitializeDetector()
        {
            _detector = new BackupFileDetector();
            _detector.ScanCompleted += OnScanCompleted;
            _detector.LogMessage += OnLogMessage;
        }

        private void UpdateControlStates()
        {
            // 根据单选按钮状态更新控件可用性
            txtSingleUrl.Enabled = radioSingleUrl.Checked;
            txtUrlList.Enabled = radioUrlList.Checked;
            btnLoadFromFile.Enabled = radioUrlList.Checked;

            // 根据扫描状态更新按钮
            btnStart.Enabled = !_isScanning;
            btnStop.Enabled = _isScanning;
            
            // 更新设置控件
            groupBoxTarget.Enabled = !_isScanning;
            groupBoxSettings.Enabled = !_isScanning;
        }

        private void radioSingleUrl_CheckedChanged(object sender, EventArgs e)
        {
            UpdateControlStates();
        }

        private void radioUrlList_CheckedChanged(object sender, EventArgs e)
        {
            UpdateControlStates();
        }

        private void btnLoadFromFile_Click(object sender, EventArgs e)
        {
            if (openFileDialog.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    var urls = _detector.LoadUrlsFromFile(openFileDialog.FileName);
                    txtUrlList.Text = string.Join(Environment.NewLine, urls);
                    AddLogMessage(string.Format("从文件加载了 {0} 个URL", urls.Count));
                }
                catch (Exception ex)
                {
                    MessageBox.Show(string.Format("加载文件失败: {0}", ex.Message), "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private async void btnStart_Click(object sender, EventArgs e)
        {
            if (_isScanning) return;

            try
            {
                // 获取扫描目标
                var targetUrls = GetTargetUrls();
                if (targetUrls.Count == 0)
                {
                    MessageBox.Show("请输入要扫描的URL", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // 应用设置
                ApplySettings();

                // 开始扫描
                _isScanning = true;
                _cancellationTokenSource = new CancellationTokenSource();
                _totalUrls = 0;
                _scannedUrls = 0;
                _foundBackups = 0;

                UpdateControlStates();
                toolStripStatusLabel.Text = "正在扫描...";
                toolStripProgressBar.Value = 0;

                // 生成所有要扫描的URL
                var allUrls = new List<string>();
                foreach (var baseUrl in targetUrls)
                {
                    var backupUrls = _detector.GenerateBackupUrls(baseUrl);
                    allUrls.AddRange(backupUrls);
                }

                _totalUrls = allUrls.Count;
                toolStripProgressBar.Maximum = _totalUrls;

                AddLogMessage(string.Format("开始扫描，共 {0} 个URL", _totalUrls));

                // 执行扫描
                await _detector.ScanUrlsAsync(allUrls, (int)numThreads.Value, _cancellationTokenSource.Token);

                AddLogMessage(string.Format("扫描完成，共发现 {0} 个备份文件", _foundBackups));
            }
            catch (Exception ex)
            {
                MessageBox.Show(string.Format("扫描过程中出现错误: {0}", ex.Message), "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                AddLogMessage(string.Format("扫描错误: {0}", ex.Message));
            }
            finally
            {
                _isScanning = false;
                UpdateControlStates();
                toolStripStatusLabel.Text = "扫描完成";
            }
        }

        private void btnStop_Click(object sender, EventArgs e)
        {
            if (_cancellationTokenSource != null && !_cancellationTokenSource.IsCancellationRequested)
            {
                _cancellationTokenSource.Cancel();
                AddLogMessage("用户取消扫描");
            }
        }

        private void btnClear_Click(object sender, EventArgs e)
        {
            listViewResults.Items.Clear();
            txtLog.Clear();
            _scannedUrls = 0;
            _foundBackups = 0;
            toolStripProgressBar.Value = 0;
            toolStripStatusLabel.Text = "就绪";
        }

        private void btnExport_Click(object sender, EventArgs e)
        {
            if (listViewResults.Items.Count == 0)
            {
                MessageBox.Show("没有可导出的结果", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            if (saveFileDialog.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    ExportResults(saveFileDialog.FileName);
                    MessageBox.Show("导出成功", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show(string.Format("导出失败: {0}", ex.Message), "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private List<string> GetTargetUrls()
        {
            var urls = new List<string>();

            if (radioSingleUrl.Checked)
            {
                var url = txtSingleUrl.Text.Trim();
                if (!string.IsNullOrEmpty(url) && Uri.IsWellFormedUriString(url, UriKind.Absolute))
                {
                    urls.Add(url);
                }
            }
            else if (radioUrlList.Checked)
            {
                var lines = txtUrlList.Text.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);
                foreach (var line in lines)
                {
                    var url = line.Trim();
                    if (!string.IsNullOrEmpty(url) && Uri.IsWellFormedUriString(url, UriKind.Absolute))
                    {
                        urls.Add(url);
                    }
                }
            }

            return urls;
        }

        private void ApplySettings()
        {
            _detector.HttpMethod = radioHead.Checked ? "HEAD" : "GET";
            _detector.Timeout = (int)numTimeout.Value;
            _detector.DelayBetweenRequests = (int)numDelay.Value;
            _detector.IgnoreSSLErrors = chkIgnoreSSL.Checked;

            // 解析自定义请求头
            var customHeaders = new Dictionary<string, string>();
            var headerText = txtCustomHeaders.Text.Trim();
            if (!string.IsNullOrEmpty(headerText))
            {
                var headers = headerText.Split(new[] { '\r', '\n', ';' }, StringSplitOptions.RemoveEmptyEntries);
                foreach (var header in headers)
                {
                    var parts = header.Split(new[] { ':' }, 2);
                    if (parts.Length == 2)
                    {
                        var key = parts[0].Trim();
                        var value = parts[1].Trim();
                        if (!string.IsNullOrEmpty(key) && !string.IsNullOrEmpty(value))
                        {
                            customHeaders[key] = value;
                        }
                    }
                }
            }
            _detector.SetCustomHeaders(customHeaders);
        }

        private void OnScanCompleted(object sender, ScanResult result)
        {
            if (InvokeRequired)
            {
                Invoke(new Action<object, ScanResult>(OnScanCompleted), sender, result);
                return;
            }

            _scannedUrls++;

            // 更新进度条
            if (_totalUrls > 0)
            {
                toolStripProgressBar.Value = Math.Min(_scannedUrls, _totalUrls);
                toolStripStatusLabel.Text = string.Format("已扫描: {0}/{1}", _scannedUrls, _totalUrls);
            }

            // 只显示成功命中备份文件的条目
            if (result.IsBackupFile)
            {
                var item = new ListViewItem(result.Url);
                item.SubItems.Add(result.StatusCode.ToString());
                item.SubItems.Add(result.ContentType);
                item.SubItems.Add(FormatFileSize(result.ContentLength));
                item.SubItems.Add(string.Format("{0}ms", result.ResponseTime));
                item.SubItems.Add("发现备份文件");
                item.BackColor = Color.LightGreen;

                listViewResults.Items.Add(item);
                _foundBackups++;
            }

            // 自动滚动到最新项
            if (listViewResults.Items.Count > 0)
            {
                listViewResults.EnsureVisible(listViewResults.Items.Count - 1);
            }
        }

        private void OnLogMessage(object sender, string message)
        {
            if (InvokeRequired)
            {
                Invoke(new Action<object, string>(OnLogMessage), sender, message);
                return;
            }

            AddLogMessage(message);
        }

        private void AddLogMessage(string message)
        {
            txtLog.AppendText(string.Format("{0}{1}", message, Environment.NewLine));
            txtLog.SelectionStart = txtLog.Text.Length;
            txtLog.ScrollToCaret();
        }

        private string FormatFileSize(long bytes)
        {
            if (bytes == 0) return "0 B";

            string[] sizes = { "B", "KB", "MB", "GB" };
            int order = 0;
            double size = bytes;

            while (size >= 1024 && order < sizes.Length - 1)
            {
                order++;
                size = size / 1024;
            }

            return string.Format("{0:0.##} {1}", size, sizes[order]);
        }

        private void ExportResults(string fileName)
        {
            var sb = new StringBuilder();

            // 添加CSV头部
            sb.AppendLine("URL,状态码,内容类型,文件大小,响应时间,检测结果");

            // 添加数据行
            foreach (ListViewItem item in listViewResults.Items)
            {
                var line = string.Join(",", item.SubItems.Cast<ListViewItem.ListViewSubItem>()
                    .Select(subItem => string.Format("\"{0}\"", subItem.Text.Replace("\"", "\"\""))));
                sb.AppendLine(line);
            }

            File.WriteAllText(fileName, sb.ToString(), Encoding.UTF8);
        }

        private void listViewResults_DoubleClick(object sender, EventArgs e)
        {
            if (listViewResults.SelectedItems.Count > 0)
            {
                var selectedItem = listViewResults.SelectedItems[0];
                var url = selectedItem.Text;
                try
                {
                    System.Diagnostics.Process.Start(url);
                }
                catch (Exception ex)
                {
                    MessageBox.Show(string.Format("无法打开链接: {0}", ex.Message), "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void menuItemCopyUrl_Click(object sender, EventArgs e)
        {
            if (listViewResults.SelectedItems.Count > 0)
            {
                var selectedItem = listViewResults.SelectedItems[0];
                var url = selectedItem.Text;
                try
                {
                    System.Windows.Forms.Clipboard.SetText(url);
                    MessageBox.Show("链接已复制到剪贴板", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show(string.Format("复制失败: {0}", ex.Message), "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void MainForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (_isScanning)
            {
                var result = MessageBox.Show("扫描正在进行中，确定要退出吗？", "确认",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.No)
                {
                    e.Cancel = true;
                    return;
                }

                if (_cancellationTokenSource != null)
                    _cancellationTokenSource.Cancel();
            }

            if (_detector != null)
                _detector.Dispose();
        }
    }
}
